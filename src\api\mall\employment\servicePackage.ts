import request from '@/config/axios'

/**
 * 服务套餐管理API接口
 */

// 轮播图信息接口
export interface ServicePackageCarousel {
  id?: number
  packageId?: number
  imageUrl: string
  sortOrder?: number
  status?: number
  createTime?: string
  updateTime?: string
}

// 特色标签信息接口
export interface ServicePackageFeature {
  id?: number
  packageId?: number
  featureName: string
  sortOrder?: number
  createTime?: string
  updateTime?: string
}

// 服务套餐信息接口
export interface ServicePackage {
  id: number
  name: string
  category: string
  thumbnail: string
  price: number
  originalPrice: number
  unit: string
  serviceDuration: string
  packageType: string
  taskSplitRule: string
  serviceDescription: string
  serviceDetails: string
  serviceProcess: string
  purchaseNotice: string
  status: string
  auditStatus?: string
  agencyId?: number
  agencyName?: string
  partnerId?: number
  partnerName?: string
  rejectReason?: string
  categoryId?: number
  advanceBookingDays: number
  timeSelectionMode: string
  appointmentMode: string
  serviceStartTime: string
  addressSetting: string
  maxBookingDays: number
  cancellationPolicy: string
  // 新增的服务时间相关字段
  serviceTimeStart?: string
  serviceTimeEnd?: string
  restDayType?: string
  serviceTimespan?: string
  // 新增的任务拆分规则相关字段
  serviceTimes?: number
  validityPeriod?: number
  validityPeriodUnit?: string
  serviceIntervalType?: string
  serviceIntervalValue?: number
  singleDurationHours?: number
  carouselList: ServicePackageCarousel[]
  featureList: ServicePackageFeature[]
  createTime: string
  updateTime: string
}

// 查询参数接口
export interface ServicePackageQueryParams {
  pageNo: number
  pageSize: number
  keyword?: string
  category?: string
  status?: string
  packageType?: string
  agency?: string
}

// 新增套餐参数接口
export interface CreateServicePackageParams {
  name: string
  category: string
  thumbnail?: string
  price: number
  originalPrice?: number
  unit: string
  serviceDuration?: string
  packageType: string
  taskSplitRule?: string
  serviceDescription?: string
  serviceDetails?: string
  serviceProcess?: string
  purchaseNotice?: string
  status?: string
  agencyId?: number
  agencyName?: string
  partnerId?: number
  partnerName?: string
  categoryId?: number
  advanceBookingDays?: number
  timeSelectionMode?: string
  appointmentMode?: string
  serviceStartTime?: string
  addressSetting?: string
  maxBookingDays?: number
  cancellationPolicy?: string
  // 新增的服务时间相关字段
  serviceTimeStart?: string
  serviceTimeEnd?: string
  restDayType?: string
  serviceTimespan?: string
  // 新增的任务拆分规则相关字段
  serviceTimes?: number
  validityPeriod?: number
  validityPeriodUnit?: string
  serviceIntervalType?: string
  serviceIntervalValue?: number
  singleDurationHours?: number
  carouselList?: ServicePackageCarousel[]
  featureList?: ServicePackageFeature[]
}

// 更新套餐参数接口
export interface UpdateServicePackageParams {
  id: number
  name: string
  category: string
  thumbnail?: string
  price: number
  originalPrice?: number
  unit: string
  serviceDuration?: string
  packageType: string
  taskSplitRule?: string
  serviceDescription?: string
  serviceDetails?: string
  serviceProcess?: string
  purchaseNotice?: string
  status?: string
  agencyId?: number
  agencyName?: string
  partnerId?: number
  partnerName?: string
  categoryId?: number
  advanceBookingDays?: number
  timeSelectionMode?: string
  appointmentMode?: string
  serviceStartTime?: string
  addressSetting?: string
  maxBookingDays?: number
  cancellationPolicy?: string
  // 新增的服务时间相关字段
  serviceTimeStart?: string
  serviceTimeEnd?: string
  restDayType?: string
  serviceTimespan?: string
  // 新增的任务拆分规则相关字段
  serviceTimes?: number
  validityPeriod?: number
  validityPeriodUnit?: string
  serviceIntervalType?: string
  serviceIntervalValue?: number
  singleDurationHours?: number
  carouselList?: ServicePackageCarousel[]
  featureList?: ServicePackageFeature[]
}

// 状态更新参数接口
export interface UpdateStatusParams {
  ids: number[]
  status: string
}

// 审核状态请求参数接口
export interface ServicePackageAuditStatusReqVO {
  auditStatus: string
  rejectReason?: string
}

// 通用响应接口
export interface ApiResponse<T> {
  code: number
  data: T
  msg: string
}

// 分页响应接口
export interface ServicePackagePageResult {
  list: ServicePackage[]
  total: number
}

/**
 * 获取服务套餐分页列表
 * @param params 查询参数
 * @returns Promise<ApiResponse<ServicePackagePageResult>>
 */
export function getServicePackageList(params: ServicePackageQueryParams) {
  return request.get<ApiResponse<ServicePackagePageResult>>({
    url: '/publicbiz/employment/service-package/page',
    params
  })
}

/**
 * 获取服务套餐详情
 * @param id 套餐ID
 * @returns Promise<ApiResponse<ServicePackage>>
 */
export function getServicePackageDetail(id: number) {
  return request.get<ApiResponse<ServicePackage>>({
    url: `/publicbiz/employment/service-package/${id}`
  })
}

/**
 * 新增服务套餐
 * @param data 套餐信息
 * @returns Promise<ApiResponse<number>>
 */
export function createServicePackage(data: CreateServicePackageParams) {
  return request.post<ApiResponse<number>>({
    url: '/publicbiz/employment/service-package/create',
    data
  })
}

/**
 * 更新服务套餐
 * @param data 更新信息
 * @returns Promise<ApiResponse<boolean>>
 */
export function updateServicePackage(data: UpdateServicePackageParams) {
  return request.put<ApiResponse<boolean>>({
    url: '/publicbiz/employment/service-package/update',
    data
  })
}

/**
 * 删除服务套餐（软删除）
 * @param id 套餐ID
 * @returns Promise<ApiResponse<boolean>>
 */
export function deleteServicePackage(id: number) {
  return request.delete<ApiResponse<boolean>>({
    url: `/admin-api/publicbiz/employment/service-package/${id}`
  })
}

/**
 * 移动服务套餐到回收站
 * @param id 套餐ID
 * @returns Promise<ApiResponse<boolean>>
 */
export function moveServicePackageToRecycle(id: number) {
  return request.delete<ApiResponse<boolean>>({
    url: `/publicbiz/employment/service-package/${id}/move-to-recycle`
  })
}

/**
 * 批量更新服务套餐状态
 * @param data 状态更新参数
 * @returns Promise<ApiResponse<boolean>>
 */
export function updateServicePackageStatus(data: UpdateStatusParams) {
  return request.put<ApiResponse<boolean>>({
    url: '/publicbiz/employment/service-package/status',
    data
  })
}

/**
 * 更新单个服务套餐状态
 * @param id 套餐ID
 * @param status 状态
 * @returns Promise<ApiResponse<boolean>>
 */
export function updateSingleServicePackageStatus(id: number, status: string) {
  return updateServicePackageStatus({ ids: [id], status })
}

/**
 * 获取套餐类型列表
 * @returns Promise<ApiResponse<string[]>>
 */
export function getPackageTypes() {
  return request.get<ApiResponse<string[]>>({
    url: '/publicbiz/employment/service-package/types'
  })
}

/**
 * 导出服务套餐列表
 * @param params 查询参数
 * @returns Promise<Blob>
 */
export function exportServicePackageList(
  params: Omit<ServicePackageQueryParams, 'pageNo' | 'pageSize'>
) {
  return request.download({
    url: '/publicbiz/employment/service-package/export',
    params
  })
}

/**
 * 审核服务套餐
 * @param id 套餐ID
 * @param auditStatus 审核状态
 * @param rejectReason 拒绝原因（拒绝时必填）
 * @returns Promise<ApiResponse<boolean>>
 */
export function auditServicePackage(id: number, auditStatus: string, rejectReason?: string) {
  const data: ServicePackageAuditStatusReqVO = { auditStatus }
  if (auditStatus === 'rejected' && rejectReason) {
    data.rejectReason = rejectReason
  }

  return request.put<ApiResponse<boolean>>({
    url: `/publicbiz/employment/service-package/${id}/audit`,
    data
  })
}

/**
 * 申请上架服务套餐
 * @param id 套餐ID
 * @returns Promise<ApiResponse<boolean>>
 */
export function shelfServicePackage(id: number) {
  return request.put<ApiResponse<boolean>>({
    url: `/publicbiz/employment/service-package/${id}/shelf`
  })
}

/**
 * 撤回服务套餐
 * @param id 套餐ID
 * @returns Promise<ApiResponse<boolean>>
 */
export function withdrawServicePackage(id: number) {
  return request.put<ApiResponse<boolean>>({
    url: `/publicbiz/employment/service-package/${id}/withdraw`
  })
}

/**
 * 获取服务套餐统计报表
 * @param partnerId 合作伙伴ID，可选参数，用于筛选特定合作伙伴的套餐统计数据
 * @returns Promise<ApiResponse<ServicePackageStatisticsRespVO>>
 */
export function getServicePackageStatistics(partnerId?: number) {
  return request.get<ApiResponse<ServicePackageStatisticsRespVO>>({
    url: '/publicbiz/employment/service-package/statistics',
    params: partnerId ? { partnerId } : {}
  })
}

// 统计报表接口
export interface ServicePackageStatisticsRespVO {
  activeCount: number // 已上架数量，status='active'的套餐数量
  pendingCount: number // 待上架数量，status='pending'的套餐数量
  deletedCount: number // 回收站数量，status='deleted'的套餐数量
  totalCount: number // 总数量，所有状态套餐的总数（不包括物理删除的数据）
  partnerId?: number // 统计范围的合作伙伴ID，如果为null则表示当前用户权限范围内的全部数据
  partnerName?: string // 统计范围的合作伙伴名称
  statisticsTime: string // 统计时间，数据生成的时间戳
}

// 操作日志接口
export interface OperateLogVO {
  id: number
  userId: number
  module: string
  name: string
  type: number
  subType?: string
  userName?: string
  action?: string
  content: string
  exts: any
  result: number
  resultMsg: string
  userIp: string
  userAgent: string
  createTime: string
}

export interface OperateLogPageResult {
  list: OperateLogVO[]
  total: number
}

/**
 * 获取操作日志分页
 * @param params 查询参数
 * @returns Promise<ApiResponse<OperateLogPageResult>>
 */
export function getOperateLogPage(params: {
  pageNo: number
  pageSize: number
  type?: string
  bizId?: number
}) {
  return request.get<ApiResponse<OperateLogPageResult>>({
    url: '/system/operate-log/page',
    params
  })
}
