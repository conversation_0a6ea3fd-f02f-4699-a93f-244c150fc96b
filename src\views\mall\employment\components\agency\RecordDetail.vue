<template>
  <div class="record-detail" v-loading="loading">
    <!-- 激励记录 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-trophy section-icon incentive"></i>
        <span class="section-title">激励记录</span>
      </div>
      <div class="section-content">
        <div class="info-grid">
          <div class="info-item">
            <span class="label">记录编号:</span>
            <span class="value">{{ record.recordId || record.id || 'REC001' }}</span>
          </div>
          <div class="info-item">
            <span class="label">记录日期:</span>
            <span class="value">{{ formatDate(record.recordDate || record.date, true) }}</span>
          </div>
          <div class="info-item">
            <span class="label">记录人:</span>
            <span class="value">{{ record.recorderName || record.recorder || '' }}</span>
          </div>
          <div class="info-item">
            <span class="label">记录时间:</span>
            <span class="value">{{
              formatDate(record.createTime || record.recordTime || record.date)
            }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 记录内容 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-file-alt section-icon"></i>
        <span class="section-title">记录内容</span>
      </div>
      <div class="section-content">
        <div class="content-item">
          <span class="label">标题:</span>
          <el-input :model-value="record.title || ''" readonly class="readonly-input" />
        </div>
        <div class="content-item">
          <span class="label">详细描述:</span>
          <el-input
            :model-value="record.description || record.reason || ''"
            type="textarea"
            :rows="4"
            readonly
            class="readonly-input"
          />
        </div>
      </div>
    </div>

    <!-- 影响信息 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-chart-line section-icon"></i>
        <span class="section-title">影响信息</span>
      </div>
      <div class="section-content">
        <div class="info-grid">
          <div class="info-item">
            <span class="label">信用分影响:</span>
            <span class="value" :class="getImpactClass(record.creditImpact)">
              {{ getCreditImpact(record.creditImpact) }}
            </span>
          </div>
          <div class="info-item">
            <span class="label">资金影响:</span>
            <span class="value">{{ getFundImpact(record.amountImpact) }}</span>
          </div>
          <div class="info-item">
            <span class="label">当前状态:</span>
            <span class="value status-active">已生效</span>
          </div>
          <div class="info-item">
            <span class="label">生效时间:</span>
            <span class="value">{{
              formatDate(record.createTime || record.effectiveTime || record.recordDate)
            }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 相关附件 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-paperclip section-icon"></i>
        <span class="section-title">相关附件</span>
      </div>
      <div class="section-content">
        <div class="attachments-grid">
          <div
            class="attachment-item"
            v-for="attachment in record.attachments || []"
            :key="attachment.id || attachment.fileName"
          >
            <div class="attachment-icon">
              <i :class="getFileIcon(attachment.type)" class="file-icon"></i>
            </div>
            <div class="attachment-info">
              <div class="file-name">{{
                attachment.fileName || attachment.name || '未知文件'
              }}</div>
              <div class="file-meta">
                <span class="file-size">{{ attachment.fileSize || attachment.size || '' }}</span>
                <span class="file-date">{{
                  formatDate(attachment.createTime || attachment.date)
                }}</span>
              </div>
            </div>
            <div class="attachment-actions">
              <el-button type="text" size="small" @click="previewFile(attachment)">
                <i class="fas fa-eye"></i> 预览
              </el-button>
              <el-button type="text" size="small" @click="downloadFile(attachment)">
                <i class="fas fa-download"></i> 下载
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 跟进记录 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-history section-icon"></i>
        <span class="section-title">跟进记录</span>
      </div>
      <div class="section-content" v-loading="followUpLoading">
        <div class="follow-up-container">
          <!-- 加载状态 -->
          <div v-if="followUpLoading" class="follow-up-loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>加载跟进记录中...</span>
          </div>

          <!-- 跟进记录列表 -->
          <div v-else-if="(record.followUps || []).length > 0" class="follow-up-timeline">
            <div class="timeline-line"></div>
            <div
              v-for="followUp in record.followUps || []"
              :key="followUp.id"
              class="follow-up-item"
            >
              <div class="follow-up-dot"></div>
              <div class="follow-up-card">
                <div class="follow-up-header">
                  <div class="follow-up-title">{{ followUp.title }}</div>
                  <div class="follow-up-time">{{ formatDate(followUp.followUpDate) }}</div>
                </div>
                <div class="follow-up-description">{{ followUp.description }}</div>
                <div class="follow-up-operator">操作人: {{ followUp.operatorName || '系统' }}</div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-follow-up">
            <el-empty description="暂无跟进记录" :image-size="60" />
          </div>
        </div>
      </div>
    </div>

    <!-- 备注信息 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-sticky-note section-icon"></i>
        <span class="section-title">备注信息</span>
      </div>
      <div class="section-content">
        <div class="remark-item">
          <div class="remark-subtitle">内部备注</div>
          <div class="remark-content">{{
            record.remarks || record.internalRemark || '暂无备注'
          }}</div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="detail-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button @click="handleEdit"> <i class="fas fa-edit"></i> 编辑记录 </el-button>
      <el-button type="primary" @click="handleAddFollowUp">
        <i class="fas fa-plus"></i> 添加跟进
      </el-button>
    </div>

    <!-- 添加跟进记录对话框 -->
    <el-dialog
      v-model="addFollowUpVisible"
      title="添加跟进记录"
      width="500px"
      :before-close="cancelFollowUp"
    >
      <el-form :model="followUpForm" label-width="80px">
        <el-form-item label="跟进标题" required>
          <el-input
            v-model="followUpForm.title"
            placeholder="请输入跟进标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="跟进描述" required>
          <el-input
            v-model="followUpForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入跟进描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="跟进日期">
          <el-date-picker
            v-model="followUpForm.followUpDate"
            type="date"
            placeholder="选择跟进日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelFollowUp">取消</el-button>
          <el-button type="primary" @click="submitFollowUp">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox, ElIcon } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import {
  getAgencyRecordDetail,
  getAgencyRecordFollowUpList,
  createAgencyRecordFollowUp,
  type FollowUpRecord
} from '@/api/mall/employment/agencyRecord'

const props = defineProps<{
  record?: any
  id?: number
}>()

const emit = defineEmits<{
  close: []
}>()

// 数据状态
const loading = ref(false)
const recordData = ref<any>(null)
const followUpLoading = ref(false)
const followUpList = ref<FollowUpRecord[]>([])
const addFollowUpVisible = ref(false)
const followUpForm = ref({
  title: '',
  description: '',
  followUpDate: new Date().toISOString().split('T')[0]
})

// 获取影响类型样式
const getImpactClass = (impact: string | number | undefined) => {
  if (impact === undefined || impact === null) return ''
  if (typeof impact === 'number') {
    if (impact > 0) return 'positive'
    if (impact < 0) return 'negative'
    return ''
  }
  if (typeof impact === 'string') {
    if (impact.includes('+')) return 'positive'
    if (impact.includes('-')) return 'negative'
  }
  return ''
}

// 获取信用分影响
const getCreditImpact = (impact: string | number | undefined) => {
  if (impact === undefined || impact === null) return '无影响'
  if (typeof impact === 'number') {
    if (impact > 0) return `+${impact} 信用分`
    if (impact < 0) return `${impact} 信用分`
    return '0 信用分'
  }
  if (typeof impact === 'string') {
    const match = impact.match(/[+-]\d+\s*信用分/)
    return match ? match[0] : '无影响'
  }
  return '无影响'
}

// 获取资金影响
const getFundImpact = (impact: string | number | undefined) => {
  if (impact === undefined || impact === null) return '无资金影响'
  if (typeof impact === 'number') {
    if (impact > 0) return `+¥${impact.toFixed(2)}`
    if (impact < 0) return `-¥${Math.abs(impact).toFixed(2)}`
    return '¥0.00'
  }
  if (typeof impact === 'string') {
    const match = impact.match(/[+-]¥[\d.]+/)
    return match ? match[0] : '无资金影响'
  }
  return '无资金影响'
}

// 格式化日期
const formatDate = (date: string | undefined, dateOnly: boolean = false) => {
  if (!date) return ''
  try {
    const dateObj = new Date(date)
    if (isNaN(dateObj.getTime())) return date

    const year = dateObj.getFullYear()
    const month = String(dateObj.getMonth() + 1).padStart(2, '0')
    const day = String(dateObj.getDate()).padStart(2, '0')

    if (dateOnly) {
      return `${year}-${month}-${day}`
    }

    const hours = String(dateObj.getHours()).padStart(2, '0')
    const minutes = String(dateObj.getMinutes()).padStart(2, '0')
    const seconds = String(dateObj.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    return date
  }
}

// 获取文件图标
const getFileIcon = (fileType: string | undefined) => {
  if (!fileType) return 'fas fa-file'
  const iconMap: Record<string, string> = {
    pdf: 'fas fa-file-pdf',
    xls: 'fas fa-file-excel',
    doc: 'fas fa-file-word',
    mp3: 'fas fa-file-audio',
    jpg: 'fas fa-file-image',
    png: 'fas fa-file-image'
  }
  return iconMap[fileType] || 'fas fa-file'
}

// 预览文件
const previewFile = (file: any) => {
  if (!file) {
    ElMessage.warning('文件信息不存在')
    return
  }

  const fileUrl = file.url || file.fileUrl || file.downloadUrl
  if (!fileUrl) {
    ElMessage.warning('文件链接不存在')
    return
  }

  const fileType = file.type || file.fileType
  const fileName = file.fileName || file.name

  // 如果是图片文件，使用图片预览
  if (fileType && fileType.startsWith('image/')) {
    // 创建图片预览弹窗
    ElMessageBox.alert(
      `<div style="text-align: center;">
        <img src="${fileUrl}" alt="${fileName}" style="max-width: 100%; max-height: 80vh; object-fit: contain;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
        <div style="display: none; color: #f56c6c; font-size: 14px;">图片加载失败，请检查文件链接</div>
        <div style="margin-top: 10px; color: #666;">${fileName}</div>
      </div>`,
      '图片预览',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '关闭',
        customClass: 'image-preview-dialog',
        center: true,
        beforeClose: (action, instance, done) => {
          // 清理可能的图片缓存
          done()
        }
      }
    )
  } else if (fileType === 'application/pdf') {
    // PDF文件，在新窗口打开
    try {
      window.open(fileUrl, '_blank')
      ElMessage.info('PDF文件已在浏览器中打开')
    } catch (error) {
      console.error('打开PDF失败:', error)
      ElMessage.error('无法预览PDF文件，请尝试下载')
    }
  } else {
    // 其他文件类型，提示下载
    ElMessage.info('该文件类型不支持预览，请使用下载功能')
  }
}

// 下载文件
const downloadFile = (file: any) => {
  if (!file) {
    ElMessage.warning('文件信息不存在')
    return
  }

  const fileUrl = file.url || file.fileUrl || file.downloadUrl
  if (!fileUrl) {
    ElMessage.warning('文件链接不存在')
    return
  }

  const fileName = file.fileName || file.name || '未知文件'
  const fileType = file.type || file.fileType

  // 显示下载提示
  ElMessage.info('正在准备下载...')

  try {
    // 对于图片和PDF等可以直接在浏览器中打开的文件，使用fetch下载
    if (fileType && (fileType.startsWith('image/') || fileType === 'application/pdf')) {
      fetch(fileUrl)
        .then((response) => {
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }
          return response.blob()
        })
        .then((blob) => {
          // 创建blob URL并下载
          const blobUrl = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = blobUrl
          link.download = fileName
          link.style.display = 'none'

          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          // 清理blob URL
          window.URL.revokeObjectURL(blobUrl)

          ElMessage.success(`文件 ${fileName} 下载成功`)
        })
        .catch((error) => {
          console.error('下载文件失败:', error)
          // 降级到直接链接下载
          fallbackDownload(fileUrl, fileName)
        })
    } else {
      // 其他文件类型，直接使用链接下载
      fallbackDownload(fileUrl, fileName)
    }
  } catch (error) {
    console.error('下载文件失败:', error)
    fallbackDownload(fileUrl, fileName)
  }
}

// 降级下载方法
const fallbackDownload = (fileUrl: string, fileName: string) => {
  try {
    const link = document.createElement('a')
    link.href = fileUrl
    link.download = fileName
    link.style.display = 'none'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('开始下载文件')
  } catch (error) {
    console.error('降级下载失败:', error)

    // 最后尝试在新窗口打开
    try {
      window.open(fileUrl, '_blank')
      ElMessage.info('无法直接下载，已在浏览器中打开文件')
    } catch (openError) {
      console.error('打开文件失败:', openError)
      ElMessage.error('下载文件失败，请检查文件链接或网络连接')
    }
  }
}

// 关闭
const handleClose = () => {
  emit('close')
}

// 编辑记录
const handleEdit = () => {
  console.log('编辑记录')
}

// 添加跟进
const handleAddFollowUp = () => {
  addFollowUpVisible.value = true
}

// 提交跟进记录
const submitFollowUp = async () => {
  if (!followUpForm.value.title.trim()) {
    ElMessage.warning('请输入跟进标题')
    return
  }

  if (!followUpForm.value.description.trim()) {
    ElMessage.warning('请输入跟进描述')
    return
  }

  try {
    const params = {
      title: followUpForm.value.title.trim(),
      description: followUpForm.value.description.trim(),
      followUpDate: followUpForm.value.followUpDate,
      recordId: props.id || recordData.value?.id
    }

    await createAgencyRecordFollowUp(params)
    ElMessage.success('添加跟进记录成功')

    // 重置表单
    followUpForm.value = {
      title: '',
      description: '',
      followUpDate: new Date().toISOString().split('T')[0]
    }

    // 关闭对话框
    addFollowUpVisible.value = false

    // 刷新跟进记录列表
    if (props.id) {
      await fetchFollowUpList(props.id)
    }
  } catch (error) {
    console.error('添加跟进记录失败:', error)
    ElMessage.error('添加跟进记录失败')
  }
}

// 取消添加跟进
const cancelFollowUp = () => {
  addFollowUpVisible.value = false
  followUpForm.value = {
    title: '',
    description: '',
    followUpDate: new Date().toISOString().split('T')[0]
  }
}

// 获取记录详情
const fetchRecordDetail = async (id: number) => {
  if (!id) return

  try {
    loading.value = true
    const response = await getAgencyRecordDetail(id)
    recordData.value = response
    console.log('获取记录详情成功:', recordData.value)

    // 获取跟进记录列表
    await fetchFollowUpList(id)
  } catch (error) {
    console.error('获取记录详情失败:', error)
    ElMessage.error('获取记录详情失败')
  } finally {
    loading.value = false
  }
}

// 获取跟进记录列表
const fetchFollowUpList = async (recordId: number) => {
  if (!recordId) return

  try {
    followUpLoading.value = true
    const response = await getAgencyRecordFollowUpList(recordId)
    followUpList.value = response || []
    console.log('获取跟进记录成功:', followUpList.value)
  } catch (error) {
    console.error('获取跟进记录失败:', error)
    ElMessage.error('获取跟进记录失败')
    followUpList.value = []
  } finally {
    followUpLoading.value = false
  }
}

// 模拟附件数据
const mockAttachments = [
  {
    id: 1,
    name: '月度评估报告',
    type: 'pdf',
    size: '2.3MB',
    date: '2024-05-31 17:45:00'
  },
  {
    id: 2,
    name: '客户满意度统计.xls',
    type: 'xls',
    size: '1.1MB',
    date: '2024-05-31 17:50:00'
  }
]

// 模拟内部备注
const mockInternalRemark = '该机构连续3个月获得优秀评级,建议继续保持并给予更多优质订单分配。'

// 合并数据
const record = computed(() => {
  const baseRecord = recordData.value || props.record || {}
  return {
    ...baseRecord,
    attachments: baseRecord.attachments || mockAttachments,
    followUps: followUpList.value, // 使用真实的跟进记录数据
    internalRemark: baseRecord.internalRemark || mockInternalRemark
  }
})

// 监听 ID 变化
watch(
  () => props.id,
  (newId) => {
    if (newId) {
      fetchRecordDetail(newId)
    }
  },
  { immediate: true }
)

// 组件挂载时获取数据
onMounted(() => {
  if (props.id) {
    fetchRecordDetail(props.id)
  }
})
</script>

<style scoped lang="scss">
.record-detail {
  padding: 20px;
  height: 100%;
  overflow-y: auto;

  .detail-section {
    margin-bottom: 30px;

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e9ecef;

      .section-icon {
        margin-right: 8px;
        font-size: 16px;

        &.incentive {
          color: #52c41a;
        }
      }

      .section-title {
        font-weight: 500;
        color: #343a40;
        font-size: 16px;
      }
    }

    .section-content {
      .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;

        .info-item {
          display: flex;
          align-items: center;

          .label {
            color: #666;
            min-width: 100px;
            margin-right: 10px;
            font-size: 14px;
          }

          .value {
            color: #333;
            font-size: 14px;

            &.positive {
              color: #52c41a;
            }

            &.negative {
              color: #ff4d4f;
            }

            &.status-active {
              color: #52c41a;
              font-weight: 500;
            }
          }
        }
      }

      .content-item {
        margin-bottom: 15px;

        .label {
          display: block;
          color: #666;
          margin-bottom: 5px;
          font-size: 14px;
        }

        .readonly-input {
          .el-input__inner {
            background: #f8f9fa;
            color: #666;
          }
        }
      }

      .attachments-grid {
        .attachment-item {
          display: flex;
          align-items: center;
          padding: 12px;
          border: 1px solid #e9ecef;
          border-radius: 6px;
          margin-bottom: 10px;

          .attachment-icon {
            margin-right: 12px;

            .file-icon {
              font-size: 24px;
              color: #666;
            }
          }

          .attachment-info {
            flex: 1;

            .file-name {
              font-weight: 500;
              color: #333;
              margin-bottom: 4px;
            }

            .file-meta {
              font-size: 12px;
              color: #999;

              .file-size {
                margin-right: 10px;
              }
            }
          }

          .attachment-actions {
            display: flex;
            gap: 8px;

            .el-button {
              padding: 4px 8px;
              font-size: 12px;
            }
          }
        }
      }

      .follow-up-container {
        .follow-up-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px 0;
          gap: 12px;
          color: #666;

          .el-icon {
            font-size: 24px;
            color: #409eff;
          }

          span {
            font-size: 14px;
          }
        }

        .follow-up-timeline {
          position: relative;
          padding-left: 20px;
          max-height: 300px; // 限制最大高度，大约3条跟进记录的高度
          overflow-y: auto; // 添加垂直滚动条

          // 自定义滚动条样式
          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;

            &:hover {
              background: #a8a8a8;
            }
          }

          .timeline-line {
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
          }

          .follow-up-item {
            position: relative;
            margin-bottom: 20px;

            &:last-child {
              margin-bottom: 0;
            }

            .follow-up-dot {
              position: absolute;
              left: -15px;
              top: 20px;
              width: 12px;
              height: 12px;
              background: #409eff;
              border-radius: 50%;
              border: 2px solid white;
              box-shadow: 0 0 0 2px #409eff;
            }

            .follow-up-card {
              background: white;
              border: 1px solid #e9ecef;
              border-radius: 8px;
              padding: 16px;
              margin-left: 20px;

              .follow-up-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;

                .follow-up-title {
                  font-weight: 600;
                  color: #333;
                  font-size: 14px;
                }

                .follow-up-time {
                  font-size: 12px;
                  color: #999;
                }
              }

              .follow-up-description {
                color: #666;
                margin-bottom: 8px;
                font-size: 14px;
                line-height: 1.5;
              }

              .follow-up-operator {
                font-size: 12px;
                color: #999;
              }
            }
          }
        }

        .empty-follow-up {
          text-align: center;
          padding: 40px 20px;
          color: #999;
        }
      }

      .remark-item {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 15px;

        .remark-subtitle {
          font-weight: 500;
          color: #333;
          margin-bottom: 8px;
        }

        .remark-content {
          color: #666;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  .detail-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;

    .el-button {
      padding: 8px 16px;

      i {
        margin-right: 4px;
      }
    }
  }
}

// 图片预览弹窗样式
:deep(.image-preview-dialog) {
  .el-message-box__content {
    padding: 20px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
  }

  .el-message-box__message {
    margin: 0;
  }

  img {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}
</style>
