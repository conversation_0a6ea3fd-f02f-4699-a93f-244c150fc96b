<template>
  <div class="incentive-records">
    <!-- 筛选和操作栏 -->
    <div class="filter-section">
      <!-- 记录类型筛选 -->
      <div class="record-type-filters">
        <el-button-group>
          <el-button
            :type="activeType === 'all' ? 'primary' : 'default'"
            @click="setActiveType('all')"
          >
            全部
          </el-button>
          <el-button
            :type="activeType === 'incentive' ? 'primary' : 'default'"
            @click="setActiveType('incentive')"
          >
            激励记录
          </el-button>
          <el-button
            :type="activeType === 'penalty' ? 'primary' : 'default'"
            @click="setActiveType('penalty')"
          >
            处罚记录
          </el-button>
        </el-button-group>
      </div>

      <!-- 日期范围筛选 -->
      <div class="date-range-filter">
        <span class="filter-label">记录日期:</span>
        <el-date-picker
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
          style="width: 240px"
        />
      </div>

      <!-- 查询按钮 -->
      <el-button type="primary" @click="handleSearch" :loading="loading">
        <i class="fas fa-search"></i>
        查询
      </el-button>

      <!-- 新增记录按钮 -->
      <el-button type="primary" @click="handleAddRecord">
        <i class="fas fa-plus"></i>
        新增记录
      </el-button>
    </div>

    <!-- 记录列表 -->
    <div class="records-list" v-loading="loading" ref="recordsListRef" @scroll="handleScroll">
      <div
        v-for="record in records"
        :key="record.id"
        class="record-item"
        :class="record.recordType"
      >
        <!-- 时间线指示器 -->
        <div class="timeline-indicator">
          <div class="timeline-line"></div>
          <div class="timeline-dot" :class="record.recordType"></div>
        </div>

        <!-- 记录内容 -->
        <div class="record-content">
          <div class="record-header">
            <div class="record-title">
              <i :class="getRecordIcon(record.recordType)" class="record-icon"></i>
              <span class="title-text">{{ record.title }}</span>
            </div>
            <div class="record-date">{{ formatDate(record.recordDate) }}</div>
          </div>

          <div class="record-details">
            <div class="detail-item">
              <span class="label">影响:</span>
              <span class="value" :class="record.recordType">
                {{ getImpactText(record) }}
              </span>
            </div>
            <div class="detail-item">
              <span class="label">事由:</span>
              <span class="value">{{ record.description }}</span>
            </div>
            <div v-if="record.status" class="detail-item">
              <span class="label">状态:</span>
              <span class="value status" :class="getStatusClass(record.status)">
                {{ getStatusText(record.status) }}
              </span>
            </div>
            <div v-if="record.attachments && record.attachments.length > 0" class="detail-item">
              <span class="label">附件:</span>
              <span class="value attachment-link"> {{ record.attachments.length }}个文件 </span>
            </div>
            <div class="detail-item record-footer">
              <span class="label">记录人:</span>
              <span class="value">{{ record.recorderName }}</span>
              <div class="record-actions">
                <el-button
                  type="text"
                  size="small"
                  @click="viewRecordDetail(record)"
                  class="detail-btn"
                >
                  查看详情
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <el-empty v-if="!loading && records.length === 0" description="暂无记录" />
    </div>

    <!-- 滚动加载更多 -->
    <div class="scroll-load-more" v-if="hasMore && !loading">
      <el-button type="text" @click="loadMore" :loading="loadingMore" class="load-more-btn">
        <i class="fas fa-chevron-down"></i>
        加载更多
      </el-button>
    </div>

    <!-- 加载完成提示 -->
    <div class="load-complete" v-if="!hasMore && records.length > 0 && !loading">
      <span class="complete-text">已加载全部记录</span>
    </div>

    <!-- 详情抽屉 -->
    <el-drawer
      v-model="detailVisible"
      :title="currentRecord?.title || '记录详情'"
      direction="rtl"
      size="600px"
      :before-close="handleCloseDetail"
    >
      <RecordDetail
        v-if="currentRecord"
        :record="currentRecord"
        :id="currentRecord.id"
        @close="handleCloseDetail"
      />
    </el-drawer>

    <!-- 新增记录对话框 -->
    <AddIncentiveRecord
      v-model:visible="addRecordVisible"
      :agency-id="props.agency?.id"
      @success="handleAddRecordSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import RecordDetail from './RecordDetail.vue'
import AddIncentiveRecord from './AddIncentiveRecord.vue'
import {
  agencyRecordApi,
  type AgencyRecordDetailVO,
  type AgencyRecordPageParams
} from '@/api/mall/employment/agencyRecord'

/** 机构数据 */
const props = defineProps<{
  agency: any
}>()

// 记录类型
const activeType = ref('all')
const dateRange = ref<[string, string] | null>(null)
const detailVisible = ref(false)
const currentRecord = ref<AgencyRecordDetailVO | null>(null)
const loading = ref(false)

// 数据相关
const records = ref<AgencyRecordDetailVO[]>([])
const total = ref(0)
const hasMore = ref(true)
const loadingMore = ref(false)
const recordsListRef = ref<HTMLElement>()

// 分页参数
const pagination = ref<AgencyRecordPageParams>({
  pageNum: 1,
  pageSize: 20,
  agencyId: props.agency?.id
})

// 获取记录列表
const getRecords = async (isLoadMore = false) => {
  try {
    if (isLoadMore) {
      loadingMore.value = true
    } else {
      loading.value = true
      // 重置分页参数
      pagination.value.pageNum = 1
      records.value = []
      hasMore.value = true
    }

    const params: AgencyRecordPageParams = {
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize,
      agencyId: props.agency?.id
    }

    // 按类型筛选
    if (activeType.value !== 'all') {
      params.recordType = activeType.value
    } else {
      // 如果是"全部"，同时查询激励和处罚两种类型
      params.recordType = 'all'
    }

    // 按日期筛选
    if (dateRange.value) {
      const [startDate, endDate] = dateRange.value
      params.startDate = startDate
      params.endDate = endDate
    }

    // 调用真实接口
    console.log('调用真实接口，参数:', params)
    const res: any = await agencyRecordApi.getPage(params)
    console.log('出参:', res)

    // 兼容不同返回形态：res 可能是 {list,total} 或 {data: { list, total }} 或 纯数组
    const rawList = Array.isArray(res)
      ? res
      : res?.list || res?.data?.list || res?.records || res?.data || []

    // 设置分页信息
    total.value = (res?.total ?? res?.data?.total ?? 0) as number

    // 数据映射，确保字段名称一致
    const newRecords = (rawList || []).map((item: any) => {
      return {
        id: item.id,
        title: item.title || item.recordTitle || item.record_title || '-',
        description:
          item.description ||
          item.recordDescription ||
          item.record_description ||
          item.reason ||
          '',
        recordType: item.recordType || item.record_type || item.type || 'incentive',
        recordDate:
          item.recordDate || item.record_date || item.createTime || item.create_time || '',
        creditImpact:
          item.creditImpact || item.credit_impact || item.creditScore || item.credit_score || 0,
        amountImpact: item.amountImpact || item.amount_impact || item.amount || item.money || 0,
        otherImpact: item.otherImpact || item.other_impact || item.other || '',
        status: item.status || 'effective',
        attachments: item.attachments || item.attachmentList || item.attachment_list || [],
        recorderName:
          item.recorderName || item.recorder_name || item.creator || item.createBy || '-',
        createTime: item.createTime || item.create_time || '',
        updateTime: item.updateTime || item.update_time || ''
      }
    })

    if (isLoadMore) {
      // 追加数据
      records.value.push(...newRecords)
    } else {
      // 替换数据
      records.value = newRecords
    }

    // 判断是否还有更多数据
    hasMore.value = records.value.length < total.value

    console.log('处理后的数据:', records.value, '是否还有更多:', hasMore.value)
  } catch (error) {
    console.error('获取记录失败:', error)
    ElMessage.error('获取记录失败')
    if (!isLoadMore) {
      // 清空数据
      records.value = []
      total.value = 0
      hasMore.value = false
    }
    throw error
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 设置活跃类型
const setActiveType = (type: string) => {
  activeType.value = type
  pagination.value.pageNum = 1
  getRecords()
}

// 滚动事件处理
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  const { scrollTop, scrollHeight, clientHeight } = target

  // 当滚动到距离底部100px时，自动加载更多
  if (
    scrollHeight - scrollTop - clientHeight < 100 &&
    hasMore.value &&
    !loadingMore.value &&
    !loading.value
  ) {
    loadMore()
  }
}

// 加载更多
const loadMore = async () => {
  if (!hasMore.value || loadingMore.value || loading.value) {
    return
  }

  pagination.value.pageNum += 1
  await getRecords(true)
}

// 获取记录图标
const getRecordIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    incentive: 'fas fa-trophy',
    penalty: 'fas fa-exclamation-triangle'
  }
  return iconMap[type] || 'fas fa-file-alt'
}

// 获取影响文本
const getImpactText = (record: AgencyRecordDetailVO) => {
  let text = ''

  if (record.creditImpact !== 0) {
    text += `${record.creditImpact > 0 ? '+' : ''}${record.creditImpact} 信用分`
  }

  if (record.amountImpact !== 0) {
    if (text) text += ', '
    text += `${record.amountImpact > 0 ? '+' : ''}¥${record.amountImpact.toFixed(2)}`
  }

  if (record.otherImpact) {
    if (text) text += ', '
    text += record.otherImpact
  }

  return text || '无影响'
}

// 获取状态样式类
const getStatusClass = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    processing: 'primary',
    completed: 'success',
    cancelled: 'info',
    effective: 'success'
  }
  return statusMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消',
    effective: '已生效'
  }
  return statusMap[status] || status
}

// 格式化日期为 yyyy-MM-dd 格式
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  } catch (error) {
    return dateString
  }
}

// 查询
const handleSearch = () => {
  pagination.value.pageNum = 1
  getRecords()
}

// 分页大小改变（保留用于调整每页数量）
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.pageNum = 1
  getRecords()
}

// 新增记录对话框状态
const addRecordVisible = ref(false)

// 新增记录
const handleAddRecord = () => {
  addRecordVisible.value = true
}

// 新增记录成功
const handleAddRecordSuccess = () => {
  // 刷新记录列表
  getRecords()
  ElMessage.success('新增记录成功')
}

// 查看记录详情
const viewRecordDetail = (record: AgencyRecordDetailVO) => {
  currentRecord.value = record
  detailVisible.value = true
}

// 关闭详情
const handleCloseDetail = () => {
  detailVisible.value = false
  currentRecord.value = null
}

onMounted(() => {
  // 初始化数据
  getRecords()
})
</script>

<style scoped lang="scss">
.incentive-records {
  padding: 20px;

  .filter-section {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;

    .record-type-filters {
      .el-button-group {
        .el-button {
          padding: 8px 16px;
        }
      }
    }

    .date-range-filter {
      display: flex;
      align-items: center;
      gap: 8px;

      .filter-label {
        font-size: 14px;
        color: #666;
        white-space: nowrap;
      }
    }

    .el-button {
      padding: 8px 16px;

      i {
        margin-right: 4px;
      }
    }
  }

  .records-list {
    max-height: 600px;
    overflow-y: auto;
    padding-right: 10px;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }

    .record-item {
      display: flex;
      margin-bottom: 20px;
      position: relative;

      &:not(:last-child) .timeline-line {
        height: 100%;
      }

      .timeline-indicator {
        position: relative;
        width: 40px;
        margin-right: 20px;

        .timeline-line {
          position: absolute;
          left: 19px;
          top: 30px;
          width: 2px;
          background: #e9ecef;
          min-height: 40px;
        }

        .timeline-dot {
          position: absolute;
          left: 10px;
          top: 10px;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          border: 3px solid #fff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &.incentive {
            background: #52c41a;
          }

          &.penalty {
            background: #ff4d4f;
          }
        }
      }

      .record-content {
        flex: 1;
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

        .record-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 15px;

          .record-title {
            display: flex;
            align-items: center;
            gap: 8px;

            .record-icon {
              font-size: 16px;
            }

            .title-text {
              font-size: 16px;
              font-weight: 500;

              .incentive & {
                color: #52c41a;
              }

              .penalty & {
                color: #ff4d4f;
              }
            }
          }

          .record-date {
            font-size: 14px;
            color: #999;
          }
        }

        .record-details {
          .detail-item {
            display: flex;
            margin-bottom: 8px;
            font-size: 14px;

            .label {
              color: #666;
              min-width: 60px;
              margin-right: 8px;
            }

            .value {
              color: #333;
              flex: 1;

              &.incentive {
                color: #52c41a;
              }

              &.penalty {
                color: #ff4d4f;
              }

              &.status {
                &.warning {
                  color: #faad14;
                }
              }

              &.attachment-link {
                color: #1890ff;
                text-decoration: underline;
                cursor: pointer;
              }
            }
          }
        }

        .record-footer {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .label {
            color: #666;
            min-width: 60px;
            margin-right: 8px;
          }

          .value {
            color: #333;
            flex: 1;
          }

          .record-actions {
            margin-left: auto;

            .el-button {
              padding: 4px 8px;
              font-size: 12px;

              &.detail-btn {
                color: #409eff;
                font-weight: 500;
                transition: all 0.2s ease;

                &:hover {
                  color: #66b1ff;
                  background-color: rgba(64, 158, 255, 0.1);
                  border-radius: 4px;
                }

                &:active {
                  color: #3a8ee6;
                }
              }
            }
          }
        }
      }
    }

    // 滚动加载更多样式
    .scroll-load-more {
      text-align: center;
      padding: 20px 0;
      border-top: 1px solid #e9ecef;
      margin-top: 20px;

      .load-more-btn {
        color: #409eff;
        font-size: 14px;
        padding: 8px 16px;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          color: #66b1ff;
          background-color: rgba(64, 158, 255, 0.1);
        }

        i {
          margin-right: 6px;
          transition: transform 0.2s ease;
        }

        &:hover i {
          transform: translateY(2px);
        }
      }
    }

    // 加载完成提示样式
    .load-complete {
      text-align: center;
      padding: 20px 0;
      border-top: 1px solid #e9ecef;
      margin-top: 20px;

      .complete-text {
        color: #999;
        font-size: 14px;
        font-style: italic;
      }
    }
  }
}
</style>
