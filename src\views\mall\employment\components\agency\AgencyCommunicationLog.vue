<template>
  <div class="communication-log">
    <!-- 新增沟通记录卡片 -->
    <div class="add-record-card">
      <div class="card-header">
        <div class="header-left">
          <i class="fas fa-plus-circle add-icon"></i>
          <span class="card-title">新增沟通记录</span>
        </div>
        <div class="header-right">
          <el-button type="text" @click="handleRefresh" :loading="loading">
            <i class="fas fa-refresh"></i>
            刷新列表
          </el-button>
        </div>
      </div>

      <div class="card-body">
        <el-form :model="addFormData" :rules="addFormRules" ref="addFormRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="沟通方式:" prop="communicationMethod">
                <el-select
                  v-model="addFormData.communicationMethod"
                  placeholder="请选择沟通方式"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in communicationMethodOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="沟通主题:" prop="subject">
                <el-input
                  v-model="addFormData.subject"
                  placeholder="例如: Q3季度合作续约"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="沟通纪要:" prop="summary">
            <el-input
              v-model="addFormData.summary"
              type="textarea"
              :rows="4"
              placeholder="在此输入详细的沟通内容..."
              style="width: 100%"
            />
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="下次跟进日期:" prop="nextFollowUpDate">
                <el-date-picker
                  v-model="addFormData.nextFollowUpDate"
                  type="date"
                  placeholder="年/月/日"
                  format="YYYY/MM/DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="添加附件:" prop="attachments">
                <div class="file-upload-container">
                  <el-upload
                    ref="uploadRef"
                    :show-file-list="false"
                    :before-upload="beforeFileUpload"
                    :http-request="handleFileUpload"
                    :on-success="onFileSuccess"
                    multiple
                    :limit="5"
                    list-type="text"
                    style="width: 100%"
                  >
                    <el-button type="primary">选择文件</el-button>
                    <template #tip>
                      <div class="el-upload__tip"> 支持图片、PDF、Word、音频、视频格式,可多选 </div>
                    </template>
                  </el-upload>
                  <!-- 已上传文件列表 -->
                  <div
                    v-if="addFormData.attachments && addFormData.attachments.length > 0"
                    class="uploaded-files"
                  >
                    <div
                      v-for="(file, index) in addFormData.attachments"
                      :key="index"
                      class="uploaded-file-item"
                    >
                      <span class="file-name">{{ file.fileName }}</span>
                      <el-button
                        type="text"
                        size="small"
                        @click="removeFile(index)"
                        style="color: #f56c6c"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button type="primary" @click="handleAddRecord" :loading="submitting">
              添加记录
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 沟通记录列表 -->
    <div
      class="communication-records"
      ref="recordsListRef"
      @scroll="handleScroll"
      v-loading="loading"
    >
      <div v-for="record in communicationRecords" :key="record.id" class="record-item">
        <!-- 时间线指示器 -->
        <div class="timeline-indicator">
          <div class="timeline-line"></div>
          <div class="timeline-dot"></div>
        </div>

        <!-- 记录内容 -->
        <div class="record-content">
          <div class="record-header">
            <div class="record-title">
              <div class="title-left">
                <span class="communication-type">{{
                  getCommunicationMethodText(record.communicationMethod)
                }}</span>
                <span class="title">{{ record.title }}</span>
              </div>
              <span class="date">{{ formatDate(record.date) }}</span>
            </div>
          </div>

          <div class="record-details">
            <div class="detail-item">
              <span class="label">纪要:</span>
              <span class="value">{{ record.summary }}</span>
            </div>
            <div v-if="record.todo" class="detail-item">
              <span class="label">待办:</span>
              <span class="value">{{ record.todo }}</span>
            </div>
            <div v-if="record.nextFollowUp" class="detail-item">
              <span class="label">下次跟进:</span>
              <span class="value">{{ formatDate(record.nextFollowUp) }}</span>
            </div>
            <div v-if="record.attachments && record.attachments.length > 0" class="detail-item">
              <span class="label">附件:</span>
              <span class="value attachment-link" @click="previewAttachments(record.attachments)">
                {{ record.attachments.length }}个文件
              </span>
            </div>
            <div class="detail-item record-footer">
              <span class="label">记录人:</span>
              <span class="value">{{ record.recorder }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <el-empty
        v-if="!loading && !loadError && communicationRecords.length === 0"
        description="暂无沟通记录"
      >
        <template #extra>
          <el-button type="primary" @click="handleRefresh">重新加载</el-button>
        </template>
      </el-empty>

      <!-- 加载失败状态 -->
      <el-empty v-if="!loading && loadError" description="加载失败，请重试">
        <template #extra>
          <el-button type="primary" @click="handleRefresh">重新加载</el-button>
        </template>
      </el-empty>

      <!-- 加载更多状态 -->
      <div v-if="loadingMore" class="loading-more-container">
        <div class="loading-more-text">加载中...</div>
      </div>

      <!-- 加载完成提示 -->
      <div v-if="!hasMore && communicationRecords.length > 0 && !loading" class="load-complete">
        <span class="complete-text">已加载全部记录</span>
      </div>
    </div>

    <!-- 详情抽屉 -->
    <el-drawer
      v-model="detailVisible"
      :title="currentRecord?.title || '沟通记录详情'"
      direction="rtl"
      size="600px"
      :before-close="handleCloseDetail"
    >
      <CommunicationDetail
        v-if="currentRecord"
        :record="currentRecord"
        @close="handleCloseDetail"
      />
    </el-drawer>

    <!-- 附件预览弹窗 -->
    <el-dialog
      v-model="attachmentPreviewVisible"
      title="附件预览"
      width="800px"
      :before-close="handleCloseAttachmentPreview"
    >
      <div class="attachment-preview-content">
        <div v-if="currentAttachments.length === 0" class="no-attachments">
          <el-empty description="暂无附件" />
        </div>
        <div v-else class="attachments-list">
          <div
            v-for="(attachment, index) in currentAttachments"
            :key="index"
            class="attachment-item"
          >
            <div class="attachment-info">
              <i class="fas fa-file attachment-icon"></i>
              <span class="attachment-name">{{ getAttachmentFileName(attachment) }}</span>
              <span class="attachment-size">{{ formatFileSize(attachment.fileSize || 0) }}</span>
            </div>
            <div class="attachment-actions">
              <el-button type="primary" size="small" @click="downloadAttachment(attachment)">
                <i class="fas fa-download"></i>
                下载
              </el-button>
              <el-button
                v-if="isPreviewableFile(attachment)"
                type="success"
                size="small"
                @click="previewFile(attachment)"
              >
                <i class="fas fa-eye"></i>
                预览
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import CommunicationDetail from './CommunicationDetail.vue'
import {
  createAgencyRecordCommunication,
  getAgencyRecordPage
} from '@/api/mall/employment/agencyRecord'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import { updateFile } from '@/api/infra/file'

// 定义组件props
interface Props {
  agency: any
}

const props = withDefaults(defineProps<Props>(), {
  agency: undefined
})

// 定义emit事件
const emit = defineEmits<{
  recordAdded: [record: any]
}>()

// 获取用户store
const userStore = useUserStore()

// 新增表单数据
const addFormData = reactive({
  communicationMethod: '',
  subject: '',
  summary: '',
  nextFollowUpDate: '',
  attachments: [] as Array<{
    fileName: string
    fileType: string
    fileSize: number
    fileUrl: string
  }>
})

// 表单验证规则
const addFormRules = {
  communicationMethod: [{ required: true, message: '请选择沟通方式', trigger: 'change' }],
  subject: [{ required: true, message: '请输入沟通主题', trigger: 'blur' }],
  summary: [{ required: true, message: '请输入沟通纪要', trigger: 'blur' }]
}

// 表单引用
const addFormRef = ref()
const uploadRef = ref()

// 提交状态
const submitting = ref(false)

// 上传配置 - 使用自定义上传

// 详情抽屉状态
const detailVisible = ref(false)
const currentRecord = ref<any>(null)

// 附件预览状态
const attachmentPreviewVisible = ref(false)
const currentAttachments = ref<any[]>([])

// 分页相关状态
const loading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)
const loadError = ref(false)
const recordsListRef = ref<HTMLElement>()

// 分页参数
const pagination = ref({
  pageNum: 1,
  pageSize: 10,
  recordType: 'communication',
  agencyId: props.agency?.id
})

// 沟通记录数据
const communicationRecords = ref<any[]>([])

/** 沟通方式选项 */
const communicationMethodOptions = ref<{ label: string; value: string }[]>([])

/** 加载沟通方式选项 */
const loadCommunicationMethodOptions = async () => {
  try {
    const res = await getDictDataPage({
      dictType: 'communication_methods',
      pageNo: 1,
      pageSize: 50
    })
    const list = res?.list ?? []
    communicationMethodOptions.value = list.map((item: any) => ({
      label: item.label,
      value: item.value
    }))
    console.log('加载沟通方式选项成功:', communicationMethodOptions.value)
  } catch (error) {
    console.error('加载沟通方式选项失败:', error)
  }
}

// 获取沟通方式文本
const getCommunicationMethodText = (method: string) => {
  if (!method) return '未知沟通方式'
  console.log('获取沟通方式文本:', method)
  // 优先从动态加载的选项中查找
  const option = communicationMethodOptions.value.find((item) => item.value === method)
  if (option) {
    return option.label
  }
}
// 获取沟通记录列表
const getCommunicationRecords = async (isLoadMore = false) => {
  try {
    loadError.value = false
    if (isLoadMore) {
      loadingMore.value = true
    } else {
      loading.value = true
      // 重置分页参数
      pagination.value.pageNum = 1
      communicationRecords.value = []
      hasMore.value = true
    }

    const params = {
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize,
      recordType: 'communication',
      agencyId: props.agency?.id
    }

    console.log('调用沟通记录接口，参数:', params)
    const res = await getAgencyRecordPage(params)
    console.log('沟通记录接口返回:', res)

    // 处理返回数据
    const rawList = res?.records || res?.data?.records || res?.list || res?.data?.list || []
    const total = res?.total || res?.data?.total || 0

    // 数据映射，确保字段名称一致
    const newRecords = (rawList || []).map((item: any) => {
      return {
        id: item.id,
        date: item.recordDate || item.record_date || item.createTime || item.create_time || '',
        title: item.title || item.recordTitle || item.record_title || '-',
        participants: item.participants || `我方(${item.recorderName || '未知'}), 对方(待补充)`,
        summary:
          item.description ||
          item.recordDescription ||
          item.record_description ||
          item.summary ||
          '',
        todo: item.todo || item.followUpItem || item.follow_up_item || null,
        nextFollowUp: item.followUpDate || item.follow_up_date || null,
        recorder:
          item.recorderName || item.recorder_name || item.creator || item.createBy || '未知',
        communicationMethod: item.communicationType,
        attachments: item.attachments || [] // 确保附件字段存在
      }
    })

    if (isLoadMore) {
      // 追加数据
      communicationRecords.value.push(...newRecords)
    } else {
      // 替换数据
      communicationRecords.value = newRecords
    }

    // 判断是否还有更多数据
    hasMore.value = communicationRecords.value.length < total

    console.log('处理后的沟通记录数据:', communicationRecords.value, '是否还有更多:', hasMore.value)
  } catch (error) {
    console.error('获取沟通记录失败:', error)
    loadError.value = true
    ElMessage.error('获取沟通记录失败')
    if (!isLoadMore) {
      // 清空数据
      communicationRecords.value = []
      hasMore.value = false
    }
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 滚动事件处理
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  const { scrollTop, scrollHeight, clientHeight } = target

  // 当滚动到距离底部100px时，自动加载更多
  if (
    scrollHeight - scrollTop - clientHeight < 100 &&
    hasMore.value &&
    !loadingMore.value &&
    !loading.value
  ) {
    // 防抖处理，避免频繁触发
    if (handleScroll.timer) {
      clearTimeout(handleScroll.timer)
    }
    handleScroll.timer = setTimeout(() => {
      loadMore()
    }, 200)
  }
}

// 为滚动函数添加防抖定时器属性
handleScroll.timer = null as any

// 加载更多
const loadMore = async () => {
  if (!hasMore.value || loadingMore.value || loading.value) {
    return
  }

  pagination.value.pageNum += 1
  await getCommunicationRecords(true)
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  } catch (error) {
    return dateString
  }
}

// 添加记录
const handleAddRecord = async () => {
  try {
    // 先进行表单验证
    await addFormRef.value?.validate()

    // 检查是否提供了agencyId
    if (!props.agency?.id) {
      ElMessage.error('缺少机构ID，无法创建沟通记录')
      return
    }

    // 显示确认对话框
    await ElMessageBox.confirm('确定要添加这条沟通记录吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })

    submitting.value = true

    // 调用API接口创建沟通记录
    const communicationData = {
      recordType: 'communication',
      communicationType: addFormData.communicationMethod,
      communicationTitle: addFormData.subject,
      communicationContent: addFormData.summary,
      participants: `我方(${userStore.getUser.nickname || '当前用户'}), 对方(待补充)`,
      agencyId: props.agency.id,
      followUpDate: addFormData.nextFollowUpDate,
      followUpItem: addFormData.nextFollowUpDate ? '跟进相关事项' : undefined,
      attachments: addFormData.attachments,
      recorderName: userStore.getUser.nickname || '当前用户' // 使用真实用户名
    }

    await createAgencyRecordCommunication(communicationData)

    // 刷新沟通记录列表
    await getCommunicationRecords()

    // 重置表单
    Object.assign(addFormData, {
      communicationMethod: '',
      subject: '',
      summary: '',
      nextFollowUpDate: '',
      attachments: []
    })

    // 清除上传组件的文件列表
    if (uploadRef.value) {
      uploadRef.value.clearFiles()
    }

    // 显示成功提示
    ElMessage.success('沟通记录添加成功')
    console.log('沟通记录添加成功')

    // 通知父组件记录已添加
    emit('recordAdded', communicationData)
  } catch (error) {
    // 检查是否是用户取消操作
    if (error === 'cancel') {
      console.log('用户取消操作')
      return
    }

    console.error('添加沟通记录失败:', error)
    // 显示更详细的错误信息
    let errorMessage = '添加沟通记录失败，请重试'
    if (error && typeof error === 'object' && 'message' in error) {
      errorMessage = `添加沟通记录失败: ${error.message}`
    }
    ElMessage.error(errorMessage)
  } finally {
    submitting.value = false
  }
}

// 文件上传前校验
const beforeFileUpload = (file: File) => {
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  return true
}

// 文件上传成功
const onFileSuccess = (response: any, file: any) => {
  if (response && response.data) {
    const newFile = {
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
      fileUrl: response.data
    }
    addFormData.attachments.push(newFile)
    ElMessage.success('文件上传成功')
  }
}

// 文件上传请求
const handleFileUpload = async (options: any) => {
  try {
    const uploadFormData = new FormData()
    uploadFormData.append('file', options.file)

    const response = await updateFile(uploadFormData)
    if (response && response.data) {
      const newFile = {
        fileName: options.file.name,
        fileType: options.file.type,
        fileSize: options.file.size,
        fileUrl: response.data
      }
      addFormData.attachments.push(newFile)
      ElMessage.success('文件上传成功')
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    ElMessage.error('文件上传失败')
  }
}

// 移除文件
const removeFile = (index: number) => {
  addFormData.attachments.splice(index, 1)
  ElMessage.success('文件已移除')
}

// 查看记录详情
const viewRecordDetail = (record: any) => {
  currentRecord.value = record
  detailVisible.value = true
}

// 关闭详情
const handleCloseDetail = () => {
  detailVisible.value = false
  currentRecord.value = null
}

// 预览附件
const previewAttachments = (attachments: any[]) => {
  if (attachments.length === 0) {
    ElMessage.warning('没有可预览的附件')
    return
  }

  currentAttachments.value = attachments
  attachmentPreviewVisible.value = true
}

// 关闭附件预览弹窗
const handleCloseAttachmentPreview = () => {
  attachmentPreviewVisible.value = false
  currentAttachments.value = []
}

// 下载附件
const downloadAttachment = async (attachment: any) => {
  if (!attachment.fileUrl) {
    ElMessage.error('附件下载地址不存在')
    return
  }

  // 获取文件名用于下载
  const fileName = getAttachmentFileName(attachment)
  console.log('开始下载附件:', fileName, attachment.fileUrl)

  try {
    // 使用fetch下载文件，确保直接下载而不是预览
    const response = await fetch(attachment.fileUrl)
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`)
    }

    const blob = await response.blob()

    // 创建blob URL
    const blobUrl = window.URL.createObjectURL(blob)

    // 创建下载链接
    const link = document.createElement('a')
    link.href = blobUrl
    link.download = fileName
    link.style.display = 'none'

    // 添加到DOM并触发下载
    document.body.appendChild(link)
    link.click()

    // 清理
    document.body.removeChild(link)
    window.URL.revokeObjectURL(blobUrl)

    ElMessage.success('附件下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败，请重试')

    // 降级方案：使用传统下载方式
    try {
      const link = document.createElement('a')
      link.href = attachment.fileUrl
      link.download = fileName
      link.target = '_blank'
      link.style.display = 'none'

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      ElMessage.success('开始下载附件')
    } catch (fallbackError) {
      console.error('降级下载也失败:', fallbackError)
      ElMessage.error('下载失败，请检查网络连接')
    }
  }
}

// 判断是否为图片文件
const isImageFile = (attachment: any) => {
  const fileName = attachment.fileName || attachment.name || attachment.originalName || ''
  if (!fileName) return false

  const fileType = fileName.split('.').pop()?.toLowerCase()
  return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileType)
}

// 预览文件 (支持图片、PDF、Word、音频、视频)
const previewFile = (attachment: any) => {
  if (!attachment.fileUrl) {
    ElMessage.error('文件预览地址不存在')
    return
  }

  // 获取文件名，支持多种可能的字段名
  const fileName = attachment.fileName || attachment.name || attachment.originalName || ''
  if (!fileName) {
    ElMessage.error('文件名不存在')
    return
  }

  // 根据文件类型打开不同的预览方式
  const fileType = fileName.split('.').pop()?.toLowerCase()
  if (fileType === 'pdf') {
    window.open(attachment.fileUrl, '_blank')
  } else if (fileType === 'doc' || fileType === 'docx') {
    window.open(attachment.fileUrl, '_blank')
  } else if (fileType === 'ppt' || fileType === 'pptx') {
    window.open(attachment.fileUrl, '_blank')
  } else if (fileType === 'xls' || fileType === 'xlsx') {
    window.open(attachment.fileUrl, '_blank')
  } else if (
    fileType === 'jpg' ||
    fileType === 'jpeg' ||
    fileType === 'png' ||
    fileType === 'gif'
  ) {
    window.open(attachment.fileUrl, '_blank')
  } else if (fileType === 'mp4' || fileType === 'mp3' || fileType === 'wav' || fileType === 'avi') {
    window.open(attachment.fileUrl, '_blank')
  } else {
    ElMessage.warning('不支持该文件类型的预览')
  }
}

// 判断文件是否可预览 (支持图片、PDF、Word、音频、视频)
const isPreviewableFile = (attachment: any) => {
  // 获取文件名，支持多种可能的字段名
  const fileName = attachment.fileName || attachment.name || attachment.originalName || ''
  if (!fileName) {
    return false
  }

  const fileType = fileName.split('.').pop()?.toLowerCase()
  return (
    fileType === 'pdf' ||
    fileType === 'doc' ||
    fileType === 'docx' ||
    fileType === 'xls' ||
    fileType === 'xlsx' ||
    fileType === 'ppt' ||
    fileType === 'pptx' ||
    fileType === 'jpg' ||
    fileType === 'jpeg' ||
    fileType === 'png' ||
    fileType === 'gif' ||
    fileType === 'mp4' ||
    fileType === 'mp3' ||
    fileType === 'wav' ||
    fileType === 'avi'
  )
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取附件文件名
const getAttachmentFileName = (attachment: any) => {
  if (attachment.name) {
    return attachment.name
  } else if (attachment.fileName) {
    return attachment.fileName
  } else if (attachment.originalName) {
    return attachment.originalName
  } else {
    return `附件${attachment.uid || attachment.id || '未知'}`
  }
}

// 刷新列表
const handleRefresh = () => {
  getCommunicationRecords()
}

// 组件挂载时获取初始数据
onMounted(async () => {
  try {
    // 先加载沟通方式选项
    await loadCommunicationMethodOptions()
    console.log('沟通方式选项加载完成，开始获取列表数据')

    // 再获取沟通记录列表
    await getCommunicationRecords()
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败，请刷新页面重试')
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (handleScroll.timer) {
    clearTimeout(handleScroll.timer)
  }
})
</script>

<style scoped lang="scss">
.communication-log {
  padding: 20px;

  .add-record-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .card-header {
      padding: 20px 20px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        display: flex;
        align-items: center;
        gap: 8px;

        .add-icon {
          color: #409eff;
          font-size: 18px;
        }

        .card-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }
      }

      .header-right {
        .el-button {
          padding: 4px 8px;
          font-size: 12px;
          color: #606266;
          display: flex;
          align-items: center;
          gap: 4px;

          &:hover {
            color: #409eff;
            background-color: #ecf5ff;
          }
        }
      }
    }

    .card-body {
      padding: 20px;

      .el-form {
        .el-form-item {
          margin-bottom: 20px;

          .el-form-item__label {
            font-weight: 500;
            color: #666;
          }

          .el-upload {
            width: 100%;

            .el-upload__tip {
              color: #999;
              font-size: 12px;
              margin-top: 8px;
            }
          }
        }
      }
    }
  }

  .communication-records {
    height: calc(100vh - 400px); /* 确保列表区域有足够高度 */
    overflow-y: auto; /* 启用滚动 */
    padding-right: 10px; /* 给滚动条留出空间 */

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }

    .record-item {
      display: flex;
      margin-bottom: 20px;
      position: relative;

      &:not(:last-child) .timeline-line {
        height: 100%;
      }

      .timeline-indicator {
        position: relative;
        width: 40px;
        margin-right: 20px;

        .timeline-line {
          position: absolute;
          left: 19px;
          top: 30px;
          width: 2px;
          background: #e9ecef;
          min-height: 40px;
        }

        .timeline-dot {
          position: absolute;
          left: 10px;
          top: 10px;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: #409eff;
          border: 3px solid #fff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }

      .record-content {
        flex: 1;
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

        .record-header {
          margin-bottom: 15px;

          .record-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 15px;

            .title-left {
              display: flex;
              align-items: center;
              gap: 8px;
            }

            .communication-type {
              font-size: 12px;
              color: #666;
              background-color: #f5f7fa;
              padding: 4px 8px;
              border-radius: 4px;
              border: 1px solid #e4e7ed;
            }

            .title {
              font-size: 16px;
              font-weight: 500;
              color: #333;
            }

            .date {
              font-size: 14px;
              color: #999;
              margin-left: auto;
            }
          }
        }

        .record-details {
          .detail-item {
            display: flex;
            margin-bottom: 8px;
            font-size: 14px;

            .label {
              color: #666;
              min-width: 80px;
              margin-right: 8px;
            }

            .value {
              color: #333;
              flex: 1;

              &.attachment-link {
                color: #409eff;
                cursor: pointer;
                text-decoration: underline;
              }
            }

            &.record-footer {
              display: flex;
              align-items: center;
              justify-content: space-between;

              .label {
                color: #666;
                min-width: 80px;
                margin-right: 8px;
              }

              .value {
                color: #333;
                flex: 1;
              }

              .record-actions {
                margin-left: auto;

                .el-button {
                  padding: 4px 8px;
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
    }

    /* 加载更多样式 */
    .loading-more-container {
      text-align: center;
      padding: 20px 0;
      border-top: 1px solid #e9ecef;
      margin-top: 20px;

      .loading-more-text {
        color: #999;
        font-size: 14px;
      }
    }

    /* 加载完成提示样式 */
    .load-complete {
      text-align: center;
      padding: 20px 0;
      border-top: 1px solid #e9ecef;
      margin-top: 20px;

      .complete-text {
        color: #999;
        font-size: 14px;
        font-style: italic;
      }
    }
  }
}

// 文件上传样式
.file-upload-container {
  .uploaded-files {
    margin-top: 10px;

    .uploaded-file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      margin-bottom: 8px;
      background-color: #f8f9fa;

      .file-name {
        color: #333;
        font-size: 14px;
        flex: 1;
        margin-right: 10px;
      }

      .el-button {
        padding: 2px 6px;
        font-size: 12px;
      }
    }
  }
}

// 自定义上传样式
:deep(.el-upload) {
  width: 100%;

  .el-upload__tip {
    color: #999;
    font-size: 12px;
    margin-top: 8px;
  }
}

// 附件预览弹窗样式
.attachment-preview-content {
  .no-attachments {
    text-align: center;
    padding: 40px 0;
  }

  .attachments-list {
    max-height: 400px;
    overflow-y: auto;

    .attachment-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      margin-bottom: 12px;
      background-color: #f8f9fa;
      transition: all 0.2s ease;

      &:hover {
        background-color: #e9ecef;
        border-color: #409eff;
      }

      .attachment-info {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;

        .attachment-icon {
          font-size: 18px;
          color: #409eff;
        }

        .attachment-name {
          font-size: 14px;
          color: #333;
          font-weight: 500;
          max-width: 300px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .attachment-size {
          font-size: 12px;
          color: #999;
          background-color: #e9ecef;
          padding: 2px 8px;
          border-radius: 10px;
        }
      }

      .attachment-actions {
        display: flex;
        gap: 8px;

        .el-button {
          padding: 6px 12px;
          font-size: 12px;

          i {
            margin-right: 4px;
          }
        }
      }
    }
  }
}
</style>
