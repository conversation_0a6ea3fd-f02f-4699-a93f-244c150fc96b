<!--
  页面名称：服务套餐表单
  功能描述：新增/编辑服务套餐表单组件，支持表单校验、提交、重置、编辑回显
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="handleClose"
    :title="drawerTitle"
    direction="rtl"
    size="900px"
    :before-close="handleClose"
    class="package-form-drawer"
  >
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
      <!-- 基础信息 -->
      <div class="form-section">
        <div class="section-header">
          <i class="el-icon-info"></i>
          <span>基础信息</span>
        </div>

        <el-form-item label="套餐名称 *" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="例如:金牌月嫂|26天贴心陪护"
            style="width: 100%"
          />
          <div class="form-tip">将显示在小程序套餐卡片的标题位置</div>
        </el-form-item>

        <el-form-item label="服务分类 *" prop="categoryId">
          <el-select
            v-model="formData.categoryId"
            placeholder="请选择服务分类"
            style="width: 100%"
            @change="handleCategoryChange"
          >
            <el-option
              v-for="item in serviceTypeOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
          <div class="form-tip">对应小程序套餐选择页的分类Tab</div>
        </el-form-item>

        <el-form-item label="套餐主图 *" prop="thumbnail">
          <div class="image-upload-container">
            <el-upload
              class="image-uploader"
              :show-file-list="false"
              :before-upload="beforeImageUpload"
              :http-request="handleImageUpload"
              :on-success="onImageSuccess"
            >
              <img v-if="formData.thumbnail" :src="formData.thumbnail" class="upload-image" />
              <div v-else class="upload-placeholder">
                <el-icon class="upload-icon"><Plus /></el-icon>
                <div class="upload-text">点击上传套餐主图</div>
              </div>
            </el-upload>
            <div class="image-upload-tips">
              <div class="tip-item">
                <i class="el-icon-info"></i>
                <span>建议尺寸160×160px</span>
              </div>
              <div class="tip-item">
                <i class="el-icon-info"></i>
                <span>显示在小程序套餐卡片左侧,建议使用清晰的服务场景图</span>
              </div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="套餐轮播图 选填" prop="carouselList">
          <div class="carousel-images-container">
            <!-- 已上传的轮播图展示 -->
            <div
              v-for="(image, index) in formData.carouselList"
              :key="index"
              class="carousel-image-item"
            >
              <img :src="image.imageUrl" class="carousel-preview-image" />
              <div class="carousel-image-actions">
                <el-button type="danger" size="small" @click="removeCarouselImage(index)"
                  >删除</el-button
                >
              </div>
            </div>

            <!-- 上传按钮 -->
            <el-upload
              v-if="formData.carouselList.length < 5"
              class="carousel-uploader"
              :show-file-list="false"
              :before-upload="beforeImageUpload"
              :http-request="handleCarouselImageUpload"
            >
              <el-icon class="upload-icon"><Plus /></el-icon>
              <div class="upload-text">添加轮播图</div>
            </el-upload>
          </div>
          <div class="form-tip">用于服务详情页顶部轮播展示,最多5张</div>
        </el-form-item>
      </div>

      <!-- 价格与规格信息 -->
      <div class="form-section">
        <div class="section-header">
          <i class="el-icon-price-tag"></i>
          <span>价格与规格信息</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="套餐价格 *" prop="price">
              <el-input-number
                v-model="formData.price"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="如: 8800"
              />
              <div class="form-tip">显示在小程序套餐卡片的价格位置,如:¥8,800</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="价格单位 *" prop="unit">
              <el-select v-model="formData.unit" placeholder="请选择单位" style="width: 100%">
                <el-option
                  v-for="item in priceUnitOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <div class="form-tip">显示在小程序价格后面,如:¥8,800/次</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 套餐类型与特色标签 -->
      <div class="form-section">
        <div class="section-header">
          <i class="el-icon-collection"></i>
          <span>套餐类型与特色标签</span>
        </div>

        <el-form-item label="套餐类型 *" prop="packageType">
          <el-select
            v-model="formData.packageType"
            placeholder="请选择套餐类型"
            style="width: 100%"
            @change="handlePackageTypeChange"
          >
            <el-option
              v-for="item in packageTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <div class="form-tip">
            长周期套餐: 如月嫂26天服务,显示"长周期套餐"标签,适合连续性服务<br />
            次数次卡套餐: 如4次收纳整理,显示"次数次卡套餐"标签,适合灵活预约服务
          </div>
        </el-form-item>

        <el-form-item label="特色标签 *" prop="featureList">
          <div class="feature-input">
            <el-input
              v-model="newFeature"
              placeholder="输入特色标签,如:24小时服务"
              style="width: 300px"
              @keyup.enter="addFeature"
            />
            <el-button type="primary" @click="addFeature">添加</el-button>
          </div>
          <div class="feature-tags">
            <el-tag
              v-for="(feature, index) in formData.featureList"
              :key="index"
              closable
              @close="removeFeature(index)"
              style="margin-right: 8px; margin-bottom: 8px"
            >
              {{ feature.featureName }}
            </el-tag>
          </div>
          <div class="form-tip">
            推荐标签:
            <el-tag
              v-for="tag in recommendedTags"
              :key="tag"
              size="small"
              style="margin-right: 5px; cursor: pointer"
              @click="addRecommendedTag(tag)"
            >
              {{ tag }}
            </el-tag>
          </div>
        </el-form-item>
      </div>

      <!-- 长周期套餐-任务拆分规则 -->
      <div v-if="showLongTermConfig" class="form-section">
        <div class="section-header">
          <i class="el-icon-date"></i>
          <span>长周期套餐 - 任务拆分规则</span>
        </div>

        <div class="config-description">
          <p>适用于月嫂、长期保洁、长期护工等服务，按时间周期执行</p>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务周期 *" prop="serviceTimes">
              <el-select
                v-model="formData.serviceTimes"
                placeholder="请选择服务周期"
                style="width: 100%"
              >
                <el-option
                  v-for="item in servicePeriodOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <div class="form-tip">如"30天"、"26天"、"90天"</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务频次 *" prop="serviceIntervalType">
              <el-select
                v-model="formData.serviceIntervalType"
                placeholder="请选择频次"
                style="width: 100%"
              >
                <el-option
                  v-for="item in serviceFrequencyOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <div class="form-tip">如"每天"、"每周"、"每月"</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="单次服务时长 *" prop="singleDurationHours">
              <el-select
                v-model="formData.singleDurationHours"
                placeholder="请选择时长"
                style="width: 100%"
              >
                <el-option
                  v-for="item in singleServiceDurationOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <div class="form-tip">如"4小时"、"8小时"、"24小时"</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="休息日设置" prop="restDayType">
              <el-select
                v-model="formData.restDayType"
                placeholder="无特殊休息日设置"
                style="width: 100%"
              >
                <el-option
                  v-for="item in restDaySettingOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <div class="form-tip">如"周日"、"法定节假日"、"周末及法定节假日"</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="服务时间 *" prop="serviceTimespan">
          <el-select
            v-model="formData.serviceTimespan"
            multiple
            placeholder="请选择服务时间（可多选）"
            style="width: 100%"
          >
            <el-option
              v-for="item in serviceTimeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <div class="form-tip">可选择多个时间段，如"9:00-13:00"、"14:00-18:00"</div>
        </el-form-item>
      </div>

      <!-- 次数次卡套餐-任务拆分规则 -->
      <div v-if="showCountCardConfig" class="form-section">
        <div class="section-header">
          <i class="el-icon-tickets"></i>
          <span>次数次卡套餐 - 任务拆分规则</span>
        </div>

        <div class="config-description">
          <p>适用于收纳整理、深度保洁、家电清洗等服务，按次数执行</p>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务次数 *" prop="serviceTimes">
              <el-select
                v-model="formData.serviceTimes"
                placeholder="请选择次数"
                style="width: 100%"
              >
                <el-option
                  v-for="item in serviceCountOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <div class="form-tip">如"4次"、"6次"、"10次"</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单次服务时长 *" prop="singleDurationHours">
              <el-select
                v-model="formData.singleDurationHours"
                placeholder="请选择时长"
                style="width: 100%"
              >
                <el-option
                  v-for="item in countSingleDurationOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <div class="form-tip">如"2小时"、"4小时"、"6小时"</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务间隔" prop="serviceIntervalType">
              <el-select
                v-model="formData.serviceIntervalType"
                placeholder="请选择服务间隔"
                style="width: 100%"
              >
                <el-option
                  v-for="item in serviceIntervalOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <div class="form-tip">如"每周1次"、"每月1次"、"灵活安排"</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="有效期 *" prop="validityPeriod">
              <el-select
                v-model="formData.validityPeriod"
                placeholder="请选择有效期"
                style="width: 100%"
              >
                <el-option
                  v-for="item in validityPeriodOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <div class="form-tip">如"90天"、"180天"、"365天"</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 服务详情 -->
      <div class="form-section">
        <div class="section-header">
          <i class="el-icon-document"></i>
          <span>服务详情</span>
        </div>

        <el-form-item label="服务描述 *" prop="serviceDescription">
          <el-input
            v-model="formData.serviceDescription"
            type="textarea"
            :rows="3"
            placeholder="简要描述服务内容和特色,如:专业月嫂技能培训,涵盖新生儿护理、产妇护理、月子餐制作等核心技能"
            style="width: 100%"
          />
          <div class="form-tip">显示在小程序服务详情页,建议100-200字</div>
        </el-form-item>

        <el-form-item label="详细服务内容" prop="serviceDetails">
          <Editor v-model:modelValue="formData.serviceDetails" style="min-height: 300px" />
          <div class="form-tip">用于小程序详情页的图文详情Tab</div>
        </el-form-item>
      </div>

      <!-- 购买须知 -->
      <div class="form-section">
        <div class="section-header">
          <i class="el-icon-warning"></i>
          <span>购买须知</span>
        </div>

        <el-form-item label="购买须知" prop="purchaseNotice">
          <el-input
            v-model="formData.purchaseNotice"
            type="textarea"
            :rows="4"
            placeholder="用户下单前需要了解的重要信息,如预约规则、取消政策等"
            style="width: 100%"
          />
          <div class="form-tip">重要提醒和注意事项</div>
        </el-form-item>
      </div>

      <!-- 预约配置 -->
      <div class="form-section">
        <div class="section-header">
          <i class="el-icon-date"></i>
          <span>预约配置</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预约时间范围 *" prop="advanceBookingDays">
              <el-select
                v-model="formData.advanceBookingDays"
                placeholder="请选择预约时间范围"
                style="width: 100%"
              >
                <el-option
                  v-for="item in appointmentTimeRangeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <div class="form-tip">如"提前7天预约"、"提前3天预约"</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务开始时间 *" prop="serviceStartTime">
              <el-select
                v-model="formData.serviceStartTime"
                placeholder="请选择服务开始时间"
                style="width: 100%"
              >
                <el-option
                  v-for="item in serviceStartTimeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <div class="form-tip">如"下单后3天内开始"、"指定日期开始"</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="时间选择模式 *" prop="timeSelectionMode">
              <el-select
                v-model="formData.timeSelectionMode"
                placeholder="请选择时间选择模式"
                style="width: 100%"
              >
                <el-option
                  v-for="item in timeSelectionModeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <div class="form-tip">选择"固定时间"或"灵活时间"</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地址设置 *" prop="addressSetting">
              <el-select
                v-model="formData.addressSetting"
                placeholder="请选择地址设置"
                style="width: 100%"
              >
                <el-option
                  v-for="item in addressSettingOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <div class="form-tip">选择"固定地址"或"可变更地址"</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="预约模式 *" prop="appointmentMode">
              <el-select
                v-model="formData.appointmentMode"
                disabled
                placeholder="根据套餐类型自动设置"
                style="width: 100%"
              >
                <el-option
                  v-for="item in appointmentModeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <div class="form-tip"
                >长周期套餐选择"开始日期预约"，次数次卡套餐选择"一次性预约全部服务次数"</div
              >
              <div class="config-description">
                <p>{{ appointmentModeDescription }}</p>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品状态:" prop="status">
              <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="已上架" value="active" />
                <el-option label="待上架" value="pending" />
                <el-option label="回收站" value="deleted" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">保存更新</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { Editor } from '@/components/Editor'
// 导入文件上传接口
import { updateFile } from '@/api/infra/file'
import {
  createServicePackage,
  updateServicePackage,
  getServicePackageDetail
} from '@/api/mall/employment/servicePackage'
import type {
  ServicePackageCarousel,
  ServicePackageFeature
} from '@/api/mall/employment/servicePackage'
// 导入合作伙伴接口
import { getActivePartnerList } from '@/api/infra/business/partner'

// 定义组件属性
interface Props {
  visible?: boolean
  isEdit?: boolean
  packageId?: number
  serviceTypeOptions?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  isEdit: false,
  packageId: 0,
  serviceTypeOptions: () => []
})

// 定义事件
const emit = defineEmits(['close', 'success'])

// 响应式数据
const submitting = ref(false)
const newFeature = ref('')
const formRef = ref<FormInstance>()

const formData = reactive({
  name: '',
  category: '',
  thumbnail: '',
  carouselList: [] as ServicePackageCarousel[],
  price: 0,
  unit: '次',
  packageType: 'count-card',
  taskSplitRule: '',
  featureList: [] as ServicePackageFeature[],
  serviceDescription: '',
  serviceDetails: '',
  serviceProcess: '',
  purchaseNotice: '',
  status: 'pending',
  categoryId: undefined,
  advanceBookingDays: 1,
  timeSelectionMode: 'fixed',
  appointmentMode: 'start-date',
  serviceStartTime: 'within-3-days',
  addressSetting: 'fixed',
  maxBookingDays: 30,
  cancellationPolicy: '',
  // 新增的服务时间相关字段
  serviceTimeStart: '',
  serviceTimeEnd: '',
  // 任务拆分规则相关字段
  serviceTimes: undefined as number | string | undefined,
  validityPeriod: undefined as number | undefined,
  validityPeriodUnit: '',
  serviceIntervalType: '',
  serviceIntervalValue: undefined,
  singleDurationHours: undefined as number | string | undefined,
  serviceTimespan: [] as string[],
  restDayType: '',
  // 次数次卡套餐任务拆分规则字段（数据库字段）
  // serviceTimes: '', // 与长周期套餐共用字段
  // singleDurationHours: '', // 与长周期套餐共用字段
  // serviceIntervalType: '', // 与长周期套餐共用字段
  validityPeriodDisplay: ''
})

// 推荐标签
const recommendedTags = ['24小时服务', '专业认证', '上门服务', '灵活预约', '品质保障']

// 表单选项数据（直接在组件内定义）
const priceUnitOptions = ref([
  { label: '次', value: '次' },
  { label: '天', value: '天' },
  { label: '月', value: '月' },
  { label: '年', value: '年' }
])

const packageTypeOptions = ref([
  { label: '长周期套餐', value: 'long-term' },
  { label: '次数次卡套餐', value: 'count-card' }
])

const appointmentTimeRangeOptions = ref([
  { label: '提前1天预约', value: 1 },
  { label: '提前3天预约', value: 3 },
  { label: '提前7天预约', value: 7 },
  { label: '提前15天预约', value: 15 }
])

const serviceStartTimeOptions = ref([
  { label: '3天内开始', value: 'within-3-days' },
  { label: '7天内开始', value: 'within-7-days' },
  { label: '15天内开始', value: 'within-15-days' }
])

const timeSelectionModeOptions = ref([
  { label: '固定时间', value: 'fixed' },
  { label: '灵活时间', value: 'flexible' }
])

const addressSettingOptions = ref([
  { label: '固定地址', value: 'fixed' },
  { label: '可变更地址', value: 'changeable' }
])

const appointmentModeOptions = ref([
  { label: '开始日期预约', value: 'start-date' },
  { label: '一次性预约全部服务次数', value: 'all-times' }
])

// 长周期套餐任务拆分规则选项
const servicePeriodOptions = ref([
  { label: '26天', value: 26 },
  { label: '30天', value: 30 },
  { label: '42天', value: 42 },
  { label: '60天', value: 60 },
  { label: '90天', value: 90 },
  { label: '180天', value: 180 }
])

const serviceFrequencyOptions = ref([
  { label: '每天', value: 'day' },
  { label: '每周', value: 'weekly' },
  { label: '每月', value: 'monthly' },
  { label: '每年', value: 'year' }
])

const singleServiceDurationOptions = ref([
  { label: '2小时', value: 2 },
  { label: '4小时', value: 4 },
  { label: '6小时', value: 6 },
  { label: '8小时', value: 8 },
  { label: '12小时', value: 12 },
  { label: '24小时', value: 24 }
])

const serviceTimeOptions = ref([
  { label: '9:00-13:00', value: '9:00-13:00' },
  { label: '14:00-18:00', value: '14:00-18:00' },
  { label: '8:00-16:00', value: '8:00-16:00' },
  { label: '8:00-20:00', value: '8:00-20:00' }
])

const restDaySettingOptions = ref([
  { label: '无特殊设置', value: 'none' },
  { label: '周六', value: 'saturday' },
  { label: '周日', value: 'sunday' },
  { label: '周末', value: 'weekend' },
  { label: '法定节假日', value: 'statutory' },
  { label: '周末及法定节假日', value: 'both' }
])

// 次数次卡套餐任务拆分规则选项
const serviceCountOptions = ref([
  { label: '1次', value: 1 },
  { label: '2次', value: 2 },
  { label: '4次', value: 4 },
  { label: '6次', value: 6 },
  { label: '8次', value: 8 },
  { label: '10次', value: 10 },
  { label: '12次', value: 12 }
])

const countSingleDurationOptions = ref([
  { label: '2小时', value: 2 },
  { label: '3小时', value: 3 },
  { label: '4小时', value: 4 },
  { label: '6小时', value: 6 },
  { label: '8小时', value: 8 }
])

const serviceIntervalOptions = ref([
  { label: '请选择服务间隔', value: '' },
  { label: '每天1次', value: 'day' },
  { label: '每周1次', value: 'weekly' },
  { label: '每月1次', value: 'monthly' },
  { label: '每年1次', value: 'year' }
])

const validityPeriodOptions = ref([
  { label: '30天', value: 30 },
  { label: '90天', value: 90 },
  { label: '180天', value: 180 },
  { label: '365天', value: 365 }
])

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入套餐名称', trigger: 'blur' }],
  categoryId: [{ required: true, message: '请选择服务分类', trigger: 'change' }],
  thumbnail: [{ required: true, message: '请上传套餐主图', trigger: 'change' }],
  price: [{ required: true, message: '请输入套餐价格', trigger: 'blur' }],
  unit: [{ required: true, message: '请选择价格单位', trigger: 'change' }],
  packageType: [{ required: true, message: '请选择套餐类型', trigger: 'change' }],
  featureList: [{ required: true, message: '请添加特色标签', trigger: 'change' }],
  serviceDescription: [{ required: true, message: '请输入服务描述', trigger: 'blur' }],
  advanceBookingDays: [{ required: true, message: '请选择预约时间范围', trigger: 'change' }],
  timeSelectionMode: [{ required: true, message: '请选择时间选择模式', trigger: 'change' }],
  appointmentMode: [{ required: true, message: '请选择预约模式', trigger: 'change' }],
  serviceStartTime: [{ required: true, message: '请选择服务开始时间', trigger: 'change' }],
  addressSetting: [{ required: true, message: '请选择地址设置', trigger: 'change' }]
}

// 计算属性
const drawerTitle = computed(() => {
  return props.isEdit ? '编辑套餐' : '添加新套餐'
})

// 判断是否显示长周期套餐配置
const showLongTermConfig = computed(() => {
  const packageType = formData.packageType

  // 多种可能的长周期套餐值
  const longTermValues = ['long-term', 'long_term', '长周期套餐', 'longterm', 'LONG_TERM']

  return longTermValues.some(
    (value) =>
      packageType === value ||
      (typeof packageType === 'string' && packageType.toLowerCase().includes('long')) ||
      (typeof packageType === 'string' && packageType.includes('长周期'))
  )
})

// 判断是否显示次数次卡套餐配置
const showCountCardConfig = computed(() => {
  const packageType = formData.packageType

  // 多种可能的次数次卡套餐值
  const countCardValues = ['count-card', 'count_card', '次数次卡套餐', 'countcard', 'COUNT_CARD']

  return countCardValues.some(
    (value) =>
      packageType === value ||
      (typeof packageType === 'string' && packageType.toLowerCase().includes('count')) ||
      (typeof packageType === 'string' && packageType.includes('次数次卡'))
  )
})

// 预约模式动态说明文本
const appointmentModeDescription = computed(() => {
  console.log('[appointmentModeDescription] 计算属性被触发')

  // 确保响应式依赖正确建立
  const packageType = formData.packageType
  console.log(
    '[appointmentModeDescription] 当前套餐类型:',
    packageType,
    '类型:',
    typeof packageType
  )

  // 多种可能的长周期套餐值
  const longTermValues = ['long-term', 'long_term', '长周期套餐', 'longterm', 'LONG_TERM']
  const isLongTerm = longTermValues.some((value) => {
    const match =
      packageType === value ||
      (typeof packageType === 'string' && packageType.toLowerCase().includes('long')) ||
      (typeof packageType === 'string' && packageType.includes('长周期'))
    if (match) {
      console.log('[appointmentModeDescription] 匹配到长周期套餐:', value, '实际值:', packageType)
    }
    return match
  })

  // 多种可能的次数次卡套餐值
  const countCardValues = ['count-card', 'count_card', '次数次卡套餐', 'countcard', 'COUNT_CARD']
  const isCountCard = countCardValues.some((value) => {
    const match =
      packageType === value ||
      (typeof packageType === 'string' && packageType.toLowerCase().includes('count')) ||
      (typeof packageType === 'string' && packageType.includes('次数次卡'))
    if (match) {
      console.log('[appointmentModeDescription] 匹配到次数次卡套餐:', value, '实际值:', packageType)
    }
    return match
  })

  console.log(
    '[appointmentModeDescription] 判断结果 - 长周期:',
    isLongTerm,
    '次数次卡:',
    isCountCard
  )

  let result = ''
  if (isLongTerm) {
    result =
      '适用于长周期套餐：用户只需选择服务开始日期，系统会根据服务周期、频次等配置自动安排后续服务时间。例如：26天月嫂服务，用户选择开始日期后，系统自动安排26天的每日服务。'
  } else if (isCountCard) {
    result =
      '适用于次数次卡套餐：用户需要一次性预约所有服务次数的具体时间。例如：4次收纳整理服务，用户需要选择4个具体的服务时间段。'
  } else {
    result = '请先选择套餐类型，系统将根据套餐类型显示相应的预约模式说明'
  }

  console.log('[appointmentModeDescription] 返回结果:', result)
  return result
})

// 预约模式自动联动逻辑
const autoSetAppointmentMode = () => {
  const packageType = formData.packageType
  console.log('[autoSetAppointmentMode] 套餐类型变化，自动设置预约模式:', packageType)

  if (packageType === 'long-term' || (packageType && packageType.includes('长周期'))) {
    formData.appointmentMode = 'start-date'
    console.log('[autoSetAppointmentMode] 设置为长周期套餐预约模式: start-date (开始日期预约)')
  } else if (packageType === 'count-card' || (packageType && packageType.includes('次数次卡'))) {
    formData.appointmentMode = 'all-times'
    console.log(
      '[autoSetAppointmentMode] 设置为次数次卡套餐预约模式: all-times (一次性预约全部服务次数)'
    )
  } else {
    formData.appointmentMode = ''
    console.log('[autoSetAppointmentMode] 清空预约模式')
  }
}

// 处理服务分类选择变化
const handleCategoryChange = (categoryId: number) => {
  if (categoryId && props.serviceTypeOptions) {
    const selectedCategory = props.serviceTypeOptions.find((item) => item.id === categoryId)
    if (selectedCategory) {
      formData.categoryId = categoryId
      formData.category = selectedCategory.name
      console.log('[handleCategoryChange] 分类选择变化:', {
        categoryId: formData.categoryId,
        category: formData.category
      })
    }
  } else {
    formData.categoryId = undefined
    formData.category = ''
  }
}

// 设置预约配置模块的默认值
const setBookingConfigDefaults = () => {
  // 套餐类型 - 默认选中第一条
  if (packageTypeOptions.value.length > 0) {
    formData.packageType = packageTypeOptions.value[0].value
  }

  // 预约时间范围 - 默认选中第一条
  if (appointmentTimeRangeOptions.value.length > 0) {
    formData.advanceBookingDays = appointmentTimeRangeOptions.value[0].value
  }

  // 时间选择模式 - 默认选中第一条
  if (timeSelectionModeOptions.value.length > 0) {
    formData.timeSelectionMode = timeSelectionModeOptions.value[0].value
  }

  // 预约模式 - 默认选中第一条
  if (appointmentModeOptions.value.length > 0) {
    formData.appointmentMode = appointmentModeOptions.value[0].value
  }

  // 服务开始时间 - 默认选中第一条
  if (serviceStartTimeOptions.value.length > 0) {
    formData.serviceStartTime = serviceStartTimeOptions.value[0].value
  }

  // 地址设置 - 默认选中第一条
  if (addressSettingOptions.value.length > 0) {
    formData.addressSetting = addressSettingOptions.value[0].value
  }
}

// 添加特色标签
const addFeature = () => {
  if (newFeature.value.trim()) {
    const featureExists = formData.featureList.some(
      (f) => f.featureName === newFeature.value.trim()
    )
    if (!featureExists) {
      formData.featureList.push({
        featureName: newFeature.value.trim(),
        sortOrder: formData.featureList.length + 1
      })
    }
    newFeature.value = ''
  }
}

// 移除特色标签
const removeFeature = (index: number) => {
  formData.featureList.splice(index, 1)
}

// 添加推荐标签
const addRecommendedTag = (tag: string) => {
  const featureExists = formData.featureList.some((f) => f.featureName === tag)
  if (!featureExists) {
    formData.featureList.push({
      featureName: tag,
      sortOrder: formData.featureList.length + 1
    })
  }
}

// 图片上传前校验
const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 主图上传处理
const handleImageUpload = async (options: any) => {
  try {
    // 使用真实 API
    const uploadFormData = new FormData()
    uploadFormData.append('file', options.file)

    const response = await updateFile(uploadFormData)
    if (response && response.data) {
      formData.thumbnail = response.data
      ElMessage.success('主图上传成功')
    }
  } catch (error) {
    console.error('主图上传失败:', error)
    ElMessage.error('主图上传失败')
  }
}

// 图片上传成功回调
const onImageSuccess = (response: any) => {
  if (response && response.data) {
    formData.thumbnail = response.data
    ElMessage.success('图片上传成功')
  }
}

// 轮播图上传处理
const handleCarouselImageUpload = async (options: any) => {
  try {
    // 使用真实 API
    const uploadFormData = new FormData()
    uploadFormData.append('file', options.file)

    const response = await updateFile(uploadFormData)
    if (response && response.data) {
      formData.carouselList.push({
        imageUrl: response.data,
        sortOrder: formData.carouselList.length + 1,
        status: 1
      })
      ElMessage.success('轮播图上传成功')
    }
  } catch (error) {
    console.error('轮播图上传失败:', error)
    ElMessage.error('轮播图上传失败')
  }
}

// 删除轮播图
const removeCarouselImage = (index: number) => {
  formData.carouselList.splice(index, 1)
  ElMessage.success('轮播图删除成功')
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    category: '',
    categoryId: undefined,
    thumbnail: '',
    carouselList: [],
    price: 0,
    unit: '次',
    packageType: 'count-card',
    taskSplitRule: '',
    featureList: [],
    serviceDescription: '',
    serviceDetails: '',
    serviceProcess: '',
    purchaseNotice: '',
    status: 'pending',
    auditStatus: 'auditing',
    advanceBookingDays: 1,
    timeSelectionMode: 'fixed',
    appointmentMode: 'start-date',
    serviceStartTime: 'within-3-days',
    addressSetting: 'fixed',
    maxBookingDays: 30,
    cancellationPolicy: '',
    // 长周期套餐任务拆分规则字段重置
    serviceTimes: undefined,
    serviceIntervalType: '',
    singleDurationHours: undefined,
    serviceTimespan: [],
    restDayType: '',
    // 次数次卡套餐任务拆分规则字段重置（与长周期套餐共用字段）
    // serviceTimes: '', // 已在上面重置
    // singleDurationHours: '', // 已在上面重置
    // serviceIntervalType: '', // 已在上面重置
    validityPeriod: undefined
  })
  newFeature.value = ''

  // 重置后设置预约配置的默认值
  setBookingConfigDefaults()

  // 重置后自动设置预约模式
  nextTick(() => {
    autoSetAppointmentMode()
  })
}

// 加载套餐详情数据
const loadPackageDetail = async () => {
  if (!props.packageId) return

  try {
    let detail: any = null

    // 使用真实 API
    const response = await getServicePackageDetail(props.packageId)
    console.log('[ServicePackageForm] 详情API响应:', response)

    // 处理不同的响应格式
    if (response && response.data) {
      detail = response.data
    } else if (response && (response as any).id) {
      detail = response
    }

    if (!detail) {
      throw new Error('无法获取套餐详情')
    }

    // 填充表单数据
    Object.assign(formData, {
      name: detail.name,
      category: detail.category,
      categoryId: detail.categoryId,
      thumbnail: detail.thumbnail,
      carouselList: detail.carouselList || [],
      price: detail.price,
      originalPrice: detail.originalPrice,
      unit: detail.unit,
      packageType: detail.packageType,
      taskSplitRule: detail.taskSplitRule,
      featureList: detail.featureList || [],
      serviceDescription: detail.serviceDescription,
      serviceDetails: detail.serviceDetails,
      serviceProcess: detail.serviceProcess,
      purchaseNotice: detail.purchaseNotice,
      status: detail.status || 'pending',
      auditStatus: detail.auditStatus || 'auditing',
      advanceBookingDays: detail.advanceBookingDays, // 直接使用数字类型
      timeSelectionMode: detail.timeSelectionMode,
      appointmentMode: detail.appointmentMode,
      serviceStartTime: detail.serviceStartTime,
      addressSetting: detail.addressSetting,
      maxBookingDays: detail.maxBookingDays,
      cancellationPolicy: detail.cancellationPolicy,
      // 任务拆分规则字段（长周期套餐和次数次卡套餐共用）
      serviceTimes: detail.serviceTimes || '',
      serviceIntervalType: detail.serviceIntervalType || '',
      singleDurationHours: detail.singleDurationHours || '',
      serviceTimespan:
        typeof detail.serviceTimespan === 'string' && detail.serviceTimespan.trim() !== ''
          ? detail.serviceTimespan
              .split(',')
              .map((item) => item.trim())
              .filter((item) => item !== '')
          : Array.isArray(detail.serviceTimespan)
            ? detail.serviceTimespan
            : [],
      restDayType: detail.restDayType || '',
      // 次数次卡套餐任务拆分规则字段（与长周期套餐共用字段）
      // serviceTimes: 已在上面处理
      // singleDurationHours: 已在上面处理
      // serviceIntervalType: 已在上面处理
      validityPeriod: detail.validityPeriod || ''
    })

    // 数据加载完成后自动设置预约模式
    nextTick(() => {
      autoSetAppointmentMode()
    })
  } catch (error) {
    console.error('获取套餐详情失败:', error)
    ElMessage.error('获取套餐详情失败')
  }
}

// 生成任务拆分规则
const generateTaskSplitRule = () => {
  const packageType = formData.packageType
  console.log('[generateTaskSplitRule] 开始生成任务拆分规则，套餐类型:', packageType)

  if (packageType === 'long-term' || packageType.includes('长周期')) {
    // 长周期套餐：服务周期 + 服务频次 + 单次服务时长 + 休息日设置
    const labels = []

    // 服务周期
    if (formData.serviceTimes) {
      const serviceTimesOption = servicePeriodOptions.value.find(
        (item) => item.value === formData.serviceTimes
      )
      if (serviceTimesOption) {
        labels.push(serviceTimesOption.label)
      }
    }

    // 服务频次
    if (formData.serviceIntervalType) {
      const frequencyOption = serviceFrequencyOptions.value.find(
        (item) => item.value === formData.serviceIntervalType
      )
      if (frequencyOption) {
        labels.push(frequencyOption.label)
      }
    }

    // 单次服务时长
    if (formData.singleDurationHours) {
      const durationOption = singleServiceDurationOptions.value.find(
        (item) => item.value === formData.singleDurationHours
      )
      if (durationOption) {
        labels.push(durationOption.label)
      }
    }

    // 休息日设置
    if (formData.restDayType) {
      const restDayOption = restDaySettingOptions.value.find(
        (item) => item.value === formData.restDayType
      )
      if (restDayOption) {
        labels.push(restDayOption.label)
      }
    }

    const result = labels.join('，')
    console.log('[generateTaskSplitRule] 长周期套餐规则生成完成:', result, '组成部分:', labels)
    return result
  } else if (packageType === 'count-card' || packageType.includes('次数次卡')) {
    // 次数次卡套餐：服务次数 + 单次服务时长 + 服务间隔 + 有效期
    const labels = []

    // 服务次数
    if (formData.serviceTimes) {
      const serviceCountOption = serviceCountOptions.value.find(
        (item) => item.value === formData.serviceTimes
      )
      if (serviceCountOption) {
        labels.push(serviceCountOption.label)
      }
    }

    // 单次服务时长
    if (formData.singleDurationHours) {
      const durationOption = countSingleDurationOptions.value.find(
        (item) => item.value === formData.singleDurationHours
      )
      if (durationOption) {
        labels.push(durationOption.label)
      }
    }

    // 服务间隔
    if (formData.serviceIntervalType) {
      const intervalOption = serviceIntervalOptions.value.find(
        (item) => item.value === formData.serviceIntervalType
      )
      if (intervalOption && intervalOption.value !== '') {
        labels.push(intervalOption.label)
      }
    }

    // 有效期
    if (formData.validityPeriod) {
      const validityOption = validityPeriodOptions.value.find(
        (item) => item.value === formData.validityPeriod
      )
      if (validityOption) {
        labels.push(validityOption.label)
      }
    }

    const result = labels.join('，')
    console.log('[generateTaskSplitRule] 次数次卡套餐规则生成完成:', result, '组成部分:', labels)
    return result
  }

  console.log('[generateTaskSplitRule] 未匹配到套餐类型，返回空字符串')
  return ''
}

// 将前端表单数据转换为后端接口数据
const transformFormDataToApi = () => {
  // 自动生成任务拆分规则
  const generatedTaskSplitRule = generateTaskSplitRule()
  console.log('[transformFormDataToApi] 自动生成的任务拆分规则:', generatedTaskSplitRule)

  const apiData = {
    ...formData,
    // 自动设置任务拆分规则
    taskSplitRule: generatedTaskSplitRule,
    // 移除前端专用字段
    serviceCount: undefined,
    countSingleDuration: undefined,
    serviceInterval: undefined,
    validityPeriodDisplay: undefined,
    // 明确处理 serviceTimespan 字段：数组转字符串
    serviceTimespan:
      formData.serviceTimespan && formData.serviceTimespan.length > 0
        ? formData.serviceTimespan.join(',')
        : '',
    // 后端接口必需的固定参数
    validityPeriodUnit: 'day',
    serviceIntervalValue: 1
  }

  // 公共字段处理（所有套餐类型都需要）
  // serviceTimespan 字段已在上面的对象初始化中处理
  console.log('[transformFormDataToApi] 关键字段转换验证:', {
    serviceTimespan: {
      原始数据: formData.serviceTimespan,
      转换后: apiData.serviceTimespan,
      类型: typeof apiData.serviceTimespan
    },
    分类信息: {
      category: apiData.category,
      categoryId: apiData.categoryId
    },
    必需参数: {
      validityPeriodUnit: apiData.validityPeriodUnit,
      serviceIntervalValue: apiData.serviceIntervalValue
    }
  })

  // 根据套餐类型设置对应的后端字段
  if (formData.packageType === 'long-term' || formData.packageType.includes('长周期')) {
    // 长周期套餐字段映射 - 字段名已与数据库一致，值已为纯数值
    if (formData.serviceTimes) {
      apiData.serviceTimes =
        typeof formData.serviceTimes === 'number'
          ? formData.serviceTimes
          : parseInt(String(formData.serviceTimes))
    }
    if (formData.singleDurationHours) {
      apiData.singleDurationHours =
        typeof formData.singleDurationHours === 'number'
          ? formData.singleDurationHours
          : parseInt(String(formData.singleDurationHours))
    }
    if (formData.serviceIntervalType) {
      // 直接使用选择的值，因为现在选项值已经是标准化的英文标识符
      apiData.serviceIntervalType = formData.serviceIntervalType
    }
    if (formData.restDayType) {
      // 直接使用选择的值，因为现在选项值已经是标准化的英文标识符
      apiData.restDayType = formData.restDayType
    }
    // 长周期套餐的固定参数设置
    apiData.serviceIntervalValue = 1
    apiData.validityPeriodUnit = 'day'
  } else if (formData.packageType === 'count-card' || formData.packageType.includes('次数次卡')) {
    // 次数次卡套餐字段映射 - 字段名已与数据库一致，值已为纯数值
    if (formData.serviceTimes) {
      apiData.serviceTimes =
        typeof formData.serviceTimes === 'number'
          ? formData.serviceTimes
          : parseInt(String(formData.serviceTimes))
    }
    if (formData.singleDurationHours) {
      apiData.singleDurationHours =
        typeof formData.singleDurationHours === 'number'
          ? formData.singleDurationHours
          : parseInt(String(formData.singleDurationHours))
    }
    if (formData.serviceIntervalType) {
      if (formData.serviceIntervalType.includes('每周')) {
        apiData.serviceIntervalType = 'weekly'
        apiData.serviceIntervalValue = 1
      } else if (formData.serviceIntervalType.includes('每月')) {
        apiData.serviceIntervalType = 'monthly'
        apiData.serviceIntervalValue = 1
      }
    }
    if (formData.validityPeriodDisplay) {
      apiData.validityPeriod = parseInt(formData.validityPeriodDisplay.replace(/[^\d]/g, ''))
      apiData.validityPeriodUnit = 'day'
    }
  }

  return apiData
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const apiData = transformFormDataToApi()

    if (props.isEdit) {
      await updateServicePackage({
        id: props.packageId,
        ...apiData
      })
      ElMessage.success('更新成功')
    } else {
      await createServicePackage(apiData)
      ElMessage.success('创建成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitting.value = false
  }
}

// 关闭抽屉
const handleClose = () => {
  emit('close')
}

// 套餐类型变化处理
const handlePackageTypeChange = (value: string) => {
  console.log('[handlePackageTypeChange] 事件触发，传入值:', value)
  console.log('[handlePackageTypeChange] formData.packageType:', formData.packageType)
  console.log('[handlePackageTypeChange] 长周期套餐显示:', showLongTermConfig.value)
  console.log('[handlePackageTypeChange] 次数次卡套餐显示:', showCountCardConfig.value)
  console.log('[handlePackageTypeChange] 预约模式说明:', appointmentModeDescription.value)

  // 自动设置预约模式
  autoSetAppointmentMode()

  // 强制触发计算属性重新计算
  nextTick(() => {
    console.log(
      '[handlePackageTypeChange] nextTick后的预约模式说明:',
      appointmentModeDescription.value
    )
    console.log('[handlePackageTypeChange] nextTick后的预约模式值:', formData.appointmentMode)
  })
}

// 监听套餐类型变化
watch(
  () => formData.packageType,
  (newVal, oldVal) => {
    console.log('[watch packageType] 套餐类型变化:', oldVal, '->', newVal)
    console.log('[watch packageType] 预约模式说明:', appointmentModeDescription.value)

    // 自动设置预约模式
    if (newVal !== oldVal) {
      autoSetAppointmentMode()
    }
  },
  { immediate: true }
)

// 监听visible变化，打开时加载数据
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      if (props.isEdit && props.packageId) {
        loadPackageDetail()
      } else {
        resetForm()
        // 确保预约配置模块有默认值
        nextTick(() => {
          setBookingConfigDefaults()
        })
      }
    }
  },
  { immediate: true }
)

// 暴露方法给父组件
defineExpose({
  resetForm,
  loadPackageDetail
})
</script>

<style scoped lang="scss">
// 表单抽屉样式
.package-form-drawer {
  :deep(.el-drawer__wrapper) {
    z-index: 2000;
  }

  :deep(.el-drawer__container) {
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  }

  :deep(.el-drawer__header) {
    padding: 20px 24px;
    border-bottom: 1px solid #e4e7ed;
    margin-bottom: 0;
    background: #fff;

    .el-drawer__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .el-drawer__close-btn {
      font-size: 18px;
      color: #909399;

      &:hover {
        color: #409eff;
      }
    }
  }

  :deep(.el-drawer__body) {
    padding: 0 !important;
    height: calc(100vh - 120px);
    overflow-y: auto;
    background: #f5f5f5 !important;
    background-color: #f5f5f5 !important;
  }

  :deep(.el-drawer__footer) {
    padding: 16px 24px;
    border-top: 1px solid #e4e7ed;
    background: #fff;
    position: sticky;
    bottom: 0;
    z-index: 10;
  }

  .form-section {
    margin-bottom: 30px;
    padding: 20px 24px;
    background: #fff;
    border-radius: 8px;
    margin: 20px 24px 30px 24px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: 600;
      color: #333;

      i {
        margin-right: 8px;
        color: #3498db;
      }
    }

    .form-tip {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
      line-height: 1.4;
    }
  }

  .image-upload-container {
    display: flex;
    align-items: flex-start;
    gap: 20px;
  }

  .image-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 140px;
    height: 140px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fafafa;
    flex-shrink: 0;

    &:hover {
      border-color: #409eff;
      background: #f0f9ff;
    }

    .upload-image {
      width: 120px;
      height: 120px;
      object-fit: cover;
      border-radius: 4px;
    }

    .upload-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 100%;
    }

    .upload-icon {
      font-size: 28px;
      color: #8c939d;
      margin-bottom: 8px;
    }

    .upload-text {
      font-size: 14px;
      color: #666;
      text-align: center;
      line-height: 1.2;
    }
  }

  .image-upload-tips {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-top: 10px;

    .tip-item {
      display: flex;
      align-items: flex-start;
      gap: 6px;
      font-size: 13px;
      color: #666;
      line-height: 1.4;

      i {
        color: #409eff;
        font-size: 14px;
        margin-top: 1px;
      }
    }
  }

  .carousel-images-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 10px;
  }

  .carousel-image-item {
    position: relative;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    overflow: hidden;
    background: #fff;

    &:hover .carousel-image-actions {
      opacity: 1;
    }
  }

  .carousel-preview-image {
    width: 120px;
    height: 80px;
    object-fit: cover;
    display: block;
  }

  .carousel-image-actions {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
  }

  .carousel-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 120px;
    height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fafafa;

    &:hover {
      border-color: #409eff;
      background: #f0f9ff;
    }

    .upload-icon {
      font-size: 24px;
      color: #8c939d;
      margin-bottom: 4px;
    }

    .upload-text {
      font-size: 12px;
      color: #666;
    }
  }

  .feature-input {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .el-button {
      margin-left: 10px;
    }
  }

  .feature-tags {
    margin-bottom: 10px;
  }

  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }

  // 长周期套餐任务拆分规则样式
  .config-description {
    margin-bottom: 20px;
    padding: 12px 16px;
    background: #f0f9ff;
    border-radius: 6px;
    border-left: 3px solid #409eff;

    p {
      margin: 0;
      font-size: 14px;
      color: #666;
      line-height: 1.5;
    }
  }

  // 确保下拉框内容正常显示
  :deep(.el-select) {
    .el-input__wrapper {
      min-width: 200px;
    }
  }

  :deep(.el-select-dropdown) {
    .el-select-dropdown__item {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>

<!-- 全局样式：强制覆盖抽屉背景色 -->
<style lang="scss">
// 针对服务套餐表单抽屉的强制样式覆盖
.package-form-drawer .el-drawer__body {
  background: #f5f5f5 !important;
  background-color: #f5f5f5 !important;
}

// 备用方案：使用更高优先级的选择器
.el-drawer__wrapper .package-form-drawer .el-drawer__body {
  background: #f5f5f5 !important;
  background-color: #f5f5f5 !important;
}

// 最强覆盖：直接针对所有包含表单的抽屉
.el-drawer__body:has(.service-package-form) {
  background: #f5f5f5 !important;
  background-color: #f5f5f5 !important;
}
</style>
