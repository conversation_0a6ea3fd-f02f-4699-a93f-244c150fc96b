<!--
  页面名称：机构旗下阿姨
  功能描述：展示机构的阿姨列表，支持筛选、查看详情等操作
-->
<template>
  <div class="agency-practitioners">
    <!-- 筛选栏 -->
    <div class="filter-bar">
      <el-input
        v-model="searchForm.keyword"
        placeholder="输入阿姨姓名/手机号"
        style="width: 200px"
        clearable
      />
      <el-select
        v-model="searchForm.serviceType"
        placeholder="所有服务类型"
        clearable
        style="width: 150px"
      >
        <el-option
          v-for="item in serviceTypeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="searchForm.platformStatus"
        placeholder="所有平台状态"
        clearable
        style="width: 150px"
      >
        <el-option
          v-for="item in platformStatusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select v-model="searchForm.rating" placeholder="所有评级" clearable style="width: 150px">
        <el-option
          v-for="item in ratingOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button type="primary" @click="onSearch">查询</el-button>
      <el-button @click="onReset">重置</el-button>
      <el-button type="success" @click="onExport">
        <i class="fas fa-download"></i> 导出列表
      </el-button>
      <el-button type="primary" @click="onAdd"> <i class="fas fa-plus"></i> 新增阿姨 </el-button>
    </div>

    <!-- 调试信息 -->
    <div
      v-if="tableData.length === 0 && !loading"
      style="
        text-align: center;
        padding: 20px;
        color: #999;
        border: 1px solid #eee;
        border-radius: 4px;
      "
    >
      暂无数据 (tableData.length: {{ tableData.length }})
    </div>

    <!-- 阿姨列表 -->
    <el-table
      v-if="tableData.length > 0"
      :data="tableData"
      style="width: 100%"
      border
      v-loading="loading"
    >
      <el-table-column prop="id" label="阿姨ID/姓名" min-width="150">
        <template #default="scope">
          <div>
            <strong>{{ scope.row.name }}</strong>
            <br />
            <small style="color: #666">{{ scope.row.id }}</small>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="rating" label="综合评级" width="120">
        <template #default="scope">
          <div>
            {{ scope.row.rating }}
            <i class="fas fa-star" style="color: #f39c12; margin-left: 4px"></i>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="serviceType" label="服务类型" width="100" />
      <el-table-column prop="totalOrders" label="累计单数" width="100" />
      <el-table-column prop="currentStatus" label="当前状态" width="120">
        <template #default="scope">
          <div>
            {{ scope.row.currentStatus || '-' }}
            <br />
            <a v-if="scope.row.currentOrderId" href="#" style="font-size: 12px; color: #409eff">
              {{ scope.row.currentOrderId }}
            </a>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="platformStatus" label="平台状态" width="120">
        <template #default="scope">
          <el-tag :type="getStatusTag(scope.row.platformStatus)" size="small">
            {{ getStatusText(scope.row.platformStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <div class="operation-buttons">
            <el-button size="small" type="primary" @click="onView(scope.row)">查看</el-button>
            <el-dropdown
              v-if="
                scope.row.platformStatus !== 'terminated' && scope.row.platformStatus !== '已解约'
              "
            >
              <button class="operation-btn more-btn">...</button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="onEdit(scope.row)">编辑</el-dropdown-item>
                  <!-- <el-dropdown-item @click="onGenerateResume(scope.row)">生成简历海报</el-dropdown-item> -->
                  <el-dropdown-item @click="onTerminate(scope.row)" divided>解约</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.pageNo"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增阿姨对话框 -->
    <AddPractitioner
      v-model:visible="addDialogVisible"
      :agency-id="agency?.id"
      :agency-name="agency?.name || ''"
      @success="onAddSuccess"
    />

    <!-- 编辑阿姨对话框 -->
    <EditPractitioner
      v-model:visible="editDialogVisible"
      :practitioner="currentPractitioner"
      @success="onEditSuccess"
    />

    <!-- 阿姨详情对话框 -->
    <PractitionerDetail v-model:visible="detailDialogVisible" :practitioner="currentPractitioner" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getPractitionerPage,
  updatePractitionerStatus,
  exportPractitionerExcel
} from '@/api/mall/employment/practitioner'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import AddPractitioner from './AddPractitioner.vue'
import EditPractitioner from './EditPractitioner.vue'
import PractitionerDetail from './PractitionerDetail.vue'

/** 机构数据 */
const props = defineProps<{
  agency: any
}>()

/** 搜索表单数据 */
const searchForm = reactive({
  keyword: '',
  serviceType: '',
  platformStatus: '',
  rating: ''
})

/** 表格数据 */
const tableData = ref([])
const loading = ref(false)

/** 分页信息 */
const pagination = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

/** 对话框状态 */
const addDialogVisible = ref(false)
const editDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const currentPractitioner = ref<any>(null)

/** 下拉选项数据 */
const serviceTypeOptions = ref<any[]>([])
const platformStatusOptions = ref<any[]>([])
const ratingOptions = ref<any[]>([])

/** 获取阿姨列表 */
const fetchList = async () => {
  try {
    loading.value = true
    const params = {
      pageNo: pagination.pageNo,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword,
      serviceType: searchForm.serviceType,
      platformStatus: searchForm.platformStatus,
      rating: searchForm.rating,
      agencyId: props.agency?.id
    }

    const res = await getPractitionerPage(params)
    console.log('[AgencyPractitioners] API响应:', res)

    // 根据实际API响应格式处理数据
    if (res && res.list) {
      // 直接返回 { list: [...], total: number } 格式
      tableData.value = res.list || []
      pagination.total = res.total || 0
    } else if (res && res.data && res.data.list) {
      // 包装在data字段中的格式
      tableData.value = res.data.list || []
      pagination.total = res.data.total || 0
    } else {
      tableData.value = []
      pagination.total = 0
    }

    console.log('[AgencyPractitioners] 处理后的数据:', {
      tableData: tableData.value,
      total: pagination.total
    })
  } catch (error) {
    console.error('获取阿姨列表失败:', error)
    ElMessage.error('获取阿姨列表失败')
  } finally {
    loading.value = false
  }
}

/** 加载数据字典选项 */
const loadDictOptions = async () => {
  try {
    console.log('[AgencyPractitioners] 开始加载数据字典选项')

    // 加载服务类型选项
    console.log('[AgencyPractitioners] 加载服务类型选项')
    const serviceTypeRes = await getDictDataPage({
      dictType: 'service_type',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    console.log('[AgencyPractitioners] 服务类型响应:', serviceTypeRes)

    serviceTypeOptions.value = (serviceTypeRes.data?.list || serviceTypeRes.list || []).map(
      (item: any) => ({
        label: item.label,
        value: item.value
      })
    )
    console.log('[AgencyPractitioners] 服务类型选项:', serviceTypeOptions.value)

    // 加载平台状态选项
    console.log('[AgencyPractitioners] 加载平台状态选项')
    const platformStatusRes = await getDictDataPage({
      dictType: 'platform_status',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    console.log('[AgencyPractitioners] 平台状态响应:', platformStatusRes)

    platformStatusOptions.value = (
      platformStatusRes.data?.list ||
      platformStatusRes.list ||
      []
    ).map((item: any) => ({
      label: item.label,
      value: item.value
    }))
    console.log('[AgencyPractitioners] 平台状态选项:', platformStatusOptions.value)

    // 加载评级选项
    console.log('[AgencyPractitioners] 加载评级选项')
    const ratingRes = await getDictDataPage({
      dictType: 'composite_rating',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    console.log('[AgencyPractitioners] 评级响应:', ratingRes)

    ratingOptions.value = (ratingRes.data?.list || ratingRes.list || []).map((item: any) => ({
      label: item.label,
      value: item.value
    }))
    console.log('[AgencyPractitioners] 评级选项:', ratingOptions.value)
  } catch (error) {
    console.error('[AgencyPractitioners] 加载数据字典失败:', error)
  }
}

/** 搜索 */
const onSearch = () => {
  pagination.pageNo = 1
  fetchList()
}

/** 重置 */
const onReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    serviceType: '',
    platformStatus: '',
    rating: ''
  })
  pagination.pageNo = 1
  fetchList()
}

/** 导出列表 */
const onExport = async () => {
  try {
    const params = {
      keyword: searchForm.keyword,
      serviceType: searchForm.serviceType,
      platformStatus: searchForm.platformStatus,
      rating: searchForm.rating,
      agencyId: props.agency?.id
    }

    const blob = await exportPractitionerExcel(params)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '阿姨列表.xlsx'
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

/** 新增阿姨 */
const onAdd = () => {
  addDialogVisible.value = true
}

/** 编辑阿姨 */
const onEdit = (row: any) => {
  currentPractitioner.value = row
  editDialogVisible.value = true
}

/** 查看阿姨详情 */
const onView = (row: any) => {
  currentPractitioner.value = row
  detailDialogVisible.value = true
}

/** 解约阿姨 */
const onTerminate = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要解约该阿姨吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updatePractitionerStatus(row.id, {
      id: row.id,
      platformStatus: '已解约',
      reason: '管理员手动解约'
    })

    ElMessage.success('解约成功')
    fetchList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('解约失败:', error)
      ElMessage.error('解约失败')
    }
  }
}

/** 新增成功回调 */
const onAddSuccess = () => {
  addDialogVisible.value = false
  fetchList()
  ElMessage.success('新增阿姨成功')
}

/** 编辑成功回调 */
const onEditSuccess = () => {
  editDialogVisible.value = false
  fetchList()
  ElMessage.success('编辑阿姨成功')
}

/** 分页大小改变 */
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.pageNo = 1
  fetchList()
}

/** 当前页改变 */
const handleCurrentChange = (page: number) => {
  pagination.pageNo = page
  fetchList()
}

/** 获取状态标签类型 */
const getStatusTag = (status: string) => {
  switch (status) {
    case 'cooperating':
    case '合作中':
      return 'success'
    case 'terminated':
    case '已解约':
      return 'info'
    case 'pending':
    case '待审核':
      return 'warning'
    default:
      return 'warning'
  }
}

/** 获取状态文本 */
const getStatusText = (status: string) => {
  const option = platformStatusOptions.value.find((item) => item.value === status)
  return option ? option.label : status
}

/** 初始化 */
onMounted(() => {
  loadDictOptions()
  // 延迟加载列表数据，确保数据字典加载完成
  setTimeout(() => {
    fetchList()
  }, 100)
})
</script>

<style scoped lang="scss">
.agency-practitioners {
  .filter-bar {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    align-items: center;
    flex-wrap: wrap;
  }

  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  .operation-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .operation-btn {
    border: 1px solid #e0e0e0;
    background: #f8f9fa;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    border: none;
    outline: none;

    &:hover {
      background: #e9ecef;
      color: #495057;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: translateY(1px);
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
  }

  .more-btn {
    min-width: 32px;
    padding: 6px 8px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
